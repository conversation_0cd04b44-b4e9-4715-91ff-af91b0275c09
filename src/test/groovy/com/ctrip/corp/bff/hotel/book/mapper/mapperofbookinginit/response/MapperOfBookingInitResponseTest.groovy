package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.response

import com.ctrip.basebiz.geolocation.service.Nationality
import com.ctrip.corp.agg.commonws.entity.GetPackageRoomListResponseType
import com.ctrip.corp.agg.hotel.common.entity.RoomBedInfoType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.RoomModeInfoType
import com.ctrip.corp.bff.framework.hotel.common.meal.DailyMealInfo
import com.ctrip.corp.bff.framework.hotel.common.meal.MealUtil
import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.CorpCustomizeUtil
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil
import com.ctrip.corp.bff.hotel.book.contract.InvoiceTipDetail
import com.ctrip.corp.bff.framework.hotel.entity.contract.ApprovalOutput
import com.ctrip.corp.bff.framework.hotel.entity.contract.GuaranteeAmountInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelAmountInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.MixPayAmountInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.PriceDetailInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.PromotionAmountInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.RoomAmountInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.RoomAttributeInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.ServiceFeeAmountInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.SummaryPriceInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.TaxAmountInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.XProductAmountInfo
import com.ctrip.corp.bff.framework.hotel.resourcetoken.enums.ConfirmTypeEnum
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CertificateInfo
import com.ctrip.corp.bff.hotel.book.common.constant.InvoiceConstant
import com.ctrip.corp.bff.hotel.book.common.enums.RcTypeEnum
import com.ctrip.corp.bff.hotel.book.common.constant.ModuleCodeConstant
import com.ctrip.corp.bff.hotel.book.common.util.TokenInfoGetUtil
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil
import com.ctrip.corp.bff.hotel.book.qconfig.entity.MemberBonusRuleEntry
import com.ctrip.corp.bff.specific.contract.BatchApprovalDefaultResponseType
import com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType
import com.ctrip.corp.hotelbook.commonws.entity.DepositDescType
import com.ctrip.corp.hotelbook.commonws.entity.DistributePaymentAmountResponseType
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedInvoiceTypeResponseType
import com.ctrip.corp.hotelbook.commonws.entity.PriceType2
import com.ctrip.corp.hotelbook.commonws.entity.ServiceChargePaymentMethodInfoType
import com.ctrip.corp.order.data.aggregation.query.contract.HotelInfoType
import com.ctrip.corp.order.data.aggregation.query.contract.HotelProductType
import com.ctrip.corp.order.data.aggregation.query.contract.StakeholderType
import com.ctrip.corp.order.managementcenter.hotel.service.contract.XOrderInsuranceEnquireType
import com.ctrip.corp.order.managementcenter.hotel.service.contract.XProductEnquireResponseType
import com.ctrip.order.reimbursement.ReimbursedInfoType
import com.ctrip.soa._21234.BasicInfo
import com.ctrip.soa._21234.SearchTripBasicInfoResponseType
import com.ctrip.soa._21234.SearchTripDetailResponseType
import corp.user.service.corpUserInfoService.InvoiceInfoType
import mockit.Mocked
import org.apache.commons.lang3.StringUtils;
import com.ctrip.arch.coreinfo.utils.StringUtil
import com.ctrip.corp.agg.commonws.entity.PackageExtendInfoType
import com.ctrip.corp.agg.commonws.entity.XProductCategoryInfoType
import com.ctrip.corp.agg.commonws.entity.XProductSkuInfoType
import com.ctrip.corp.agg.commonws.entity.XProductSpuInfoType
import com.ctrip.corp.agg.commonws.entity.XProductStaticInfoType
import com.ctrip.corp.agg.hotel.common.entity.BedWidthInfoType
import com.ctrip.corp.agg.hotel.common.entity.SubBedInfoType
import com.ctrip.corp.agg.hotel.expense.contract.model.BaseChargeAmount
import com.ctrip.corp.agg.hotel.expense.contract.model.ChargeAmountInfoType
import com.ctrip.corp.agg.hotel.expense.contract.model.ServiceChargeInfoType
import com.ctrip.corp.agg.hotel.expense.contract.model.ServiceChargePriceType
import com.ctrip.corp.agg.hotel.roomavailable.entity.*
import com.ctrip.corp.agg.hotel.roomavailable.entity.AgreementGiftInfoType
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookingRulesType
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailResponseType
import com.ctrip.corp.agg.hotel.roomavailable.entity.GiftDetailInfoType
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelBrandItem
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelItem
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelRatePlan
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomDailyInfo
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomItem
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomMealEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.SalePromotionEntity
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckAgreementRcInfoType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckBookAheadRcInfoType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckControlResultType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckItemsResultType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckOverStandardRcInfoType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckRcResultType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckResultType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType
import com.ctrip.corp.agg.hotel.tmc.entity.FinalFloatingAmountSettingType
import com.ctrip.corp.agg.hotel.tmc.entity.FinalPolicyType
import com.ctrip.corp.agg.hotel.tmc.entity.GetHotelTravelPolicyResponseType
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig
import com.ctrip.corp.bff.framework.hotel.common.qconfig.entity.DefaultRoomNumberInfoConfig
import com.ctrip.corp.bff.framework.hotel.common.qconfig.entity.RoomNumberInfoConfig
import com.ctrip.corp.bff.framework.hotel.common.util.PersonAccountUtil
import com.ctrip.corp.bff.framework.hotel.common.util.TimeUtil
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfHotelTravelPolicy
import com.ctrip.corp.bff.framework.hotel.entity.contract.CancelPolicyInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.ConfirmDetailExtend
import com.ctrip.corp.bff.framework.hotel.entity.contract.ConfirmDetailInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceDetailInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelRcInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.InitConfigInfo
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.*
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil
import com.ctrip.corp.bff.framework.template.common.serialize.ProtobufSerializerUtil
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.MiceInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.hotel.book.common.builder.BookingInitAssembleRequest
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant
import com.ctrip.corp.bff.hotel.book.common.constant.InitConfigInfoConstant
import com.ctrip.corp.bff.hotel.book.common.enums.HotelBalanceTypeEnum
import com.ctrip.corp.bff.hotel.book.common.enums.HotelGuaranteeTypeEnum
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum
import com.ctrip.corp.bff.hotel.book.common.enums.PaymentGuaranteePolyEnum
import com.ctrip.corp.bff.hotel.book.common.enums.RoomAttributeEnum
import com.ctrip.corp.bff.hotel.book.common.enums.RoomPropertyEnum
import com.ctrip.corp.bff.hotel.book.common.signature.param.ApprovalInfoBO
import com.ctrip.corp.bff.hotel.book.common.signature.param.BookInfoBO
import com.ctrip.corp.bff.hotel.book.common.util.BookingInitUtil
import com.ctrip.corp.bff.hotel.book.common.util.HotelRCUtil
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.*
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.bff.hotel.book.contract.BookingInitResponseType
import com.ctrip.corp.bff.hotel.book.contract.GiftInfo
import com.ctrip.corp.bff.hotel.book.contract.OfflineInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.MapperOfCreateOrderRequestType
import com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response.MapperOfFinishInfoResponse
import com.ctrip.corp.bff.hotel.book.qconfig.entity.CustomizedSharkConfig
import com.ctrip.corp.bff.hotel.book.sharkmock.SharkMockUtil
import com.ctrip.corp.bff.payment.contract.PaymentOrderCreateResponseType
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType
import com.ctrip.corp.hotelbook.commonws.entity.CanceDescDetailType
import com.ctrip.corp.hotelbook.commonws.entity.CancelPolicyDescType
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType
import com.ctrip.corp.hotelbook.commonws.entity.GuaranteeMethodInfoType
import com.ctrip.corp.hotelbook.commonws.entity.PaymentMethodInfoType
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType
import com.ctrip.corp.order.paymentcenter.bill.contract.QueryPaymentBillConfigResponseType
import com.ctrip.model.CalculateServiceChargeV2ResponseType
import com.ctrip.model.GetRoomChargePolicyListResponseType
import com.ctrip.order.reimbursement.ReimbursementDetailInfoType
import com.ctrip.soa._21685.PayConfigResponseType
import com.ctrip.soa.platform.sps.InvoiceService.v1.GetInvoiceTitlesResponse
import corp.settlement.service.invoice.corpinvoice.CorpOrderInvoiceDetailInfoQueryResponse
import corp.settlement.service.invoice.corpinvoice.HotelFeeInfo
import corp.settlement.service.invoice.corpinvoice.HotelInvoiceDetailInfo
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.CorpAccountQueryService.QueryIndividualAccountResponseType
import corp.user.service.corp4jservice.GetReasoncodesResponseType
import corp.user.service.corp4jservice.ReasoncodeInfo
import corp.user.service.corpUserInfoService.GetContactInvoiceDefaultInfoResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import corp.user.service.group4j.accounts.BizModeBindRelationData
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import org.junit.Assert
import org.junit.Test
import spock.lang.Specification
import spock.lang.Unroll
import java.time.LocalDateTime
import java.time.Month
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil
import com.ctrip.corp.bff.hotel.book.common.builder.BookingInitAssembleRequest
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ModuleInfo
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil


/**
 * <AUTHOR>
 * @Date 2024/12/2 15:14
 */
class MapperOfBookingInitResponseTest extends Specification {


    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def testBuildHotelAttributeInfos() {
        when:
        MapperOfBookingInitResponse.BookingHotelInfoBuilder builder = new MapperOfBookingInitResponse.BookingHotelInfoBuilder(null)
        then:
        Assert.assertNotNull(builder.buildHotelAttributeInfos(new HotelItem(), new RoomItem()))
        Assert.assertNotNull(builder.buildHotelPositionInfo(new HotelItem(), null))
    }

    @Unroll
    def "testBuildCityArea with different cityId values"() {
        given:
        MapperOfBookingInitResponse mapper = new MapperOfBookingInitResponse()
        when: "cityId is #cityId"
        def result = new MapperOfBookingInitResponse.BookingHotelInfoBuilder().buildCityArea(cityId)

        then: "the result should be #expectedResult"
        result == expectedResult

        where:
        cityId || expectedResult
        22249  || "MAIN_LAND"
        null   || null
    }


    @Unroll
    def "testGetMaxRoom with different scenarios"() {
        given:
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(maxBookingRoomNum: maxBookingRoomNum, balanceType: "PP"))))
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        ResourceToken resourceToken = new ResourceToken(hotelResourceToken: new HotelResourceToken(hotelGeoInfoResourceToken: new HotelGeoInfoResourceToken(cityId: 22249)))
        IntegrationSoaRequestType integrationSoaRequestType = new IntegrationSoaRequestType()
        RoomNumberInfoConfig roomNumberInfoConfig = new RoomNumberInfoConfig(defaultRoomNumberInfoConfig: new DefaultRoomNumberInfoConfig(domesticHotelMaxBookNumberApp: maxRoomNumber))

        when: "calling getMaxRoom with specific parameters"
        def result = new MapperOfBookingInitResponse.BookingConfigInfoBuilder().getMaxRoom(checkAvailInfo, resourceToken, integrationSoaRequestType, roomNumberInfoConfig, new CorpPayInfo("public"))

        then: "the result should be as expected"
        result == expectedResult

        where:
        maxBookingRoomNum | maxRoomNumber || expectedResult
        5                 | 6             || 5
        2                 | 1             || 1
        0                 | 6             || 0
    }

    @Unroll
    def "needReloadedInvoice"() {
        given:
        expect:
        result == new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildNeedReloadedInvoice(request, approvalInput, bookInitToken)
        where:
        request                                                                                                                      | approvalInput                              | bookInitToken                              || result
        new BookingInitRequestType()                                                                                                 | new ApprovalInput(subApprovalNo: "newNo")  | new BookInitToken(approvalSubNo: "oldNo")  || "F"
        new BookingInitRequestType()                                                                                                 | new ApprovalInput(subApprovalNo: "")       | new BookInitToken(approvalSubNo: "oldNo")  || "F"
        new BookingInitRequestType()                                                                                                 | new ApprovalInput(subApprovalNo: "sameNo") | new BookInitToken(approvalSubNo: "sameNo") || "T"
        new BookingInitRequestType()                                                                                                 | new ApprovalInput(subApprovalNo: "")       | new BookInitToken(approvalSubNo: null)     || "T"
        new BookingInitRequestType(strategyInfos: Arrays.asList(new StrategyInfo(strategyKey: "FIRST_REQUEST", strategyValue: "T"))) | new ApprovalInput(subApprovalNo: "newNo")  | new BookInitToken(approvalSubNo: "oldNo")  || "T"
    }

    @Unroll
    def "testEmergencyBookChanged with different scenarios"() {
        given:
        MapperOfBookingInitResponse.BookingConfigInfoBuilder builder = new MapperOfBookingInitResponse.BookingConfigInfoBuilder()
        ApprovalInput approvalInput = new ApprovalInput()
        BookInitToken bookInitToken = new BookInitToken()

        approvalInput.setEmergency(newEmergencyBook)
        bookInitToken.setEmergencyBook(oldEmergencyBook)

        when: "calling emergencyBookChanged with specific parameters"
        def result = builder.emergencyBookChanged(approvalInput, bookInitToken)

        then: "the result should be as expected"
        result == expectedResult

        where:
        oldEmergencyBook | newEmergencyBook || expectedResult
        "T"              | "T"              || false
        "T"              | "F"              || true
        "F"              | "T"              || true
        null             | "T"              || true
        "T"              | null             || true
        ""               | "T"              || true
        "T"              | ""               || true
        null             | null             || false
    }

    @Unroll
    def "testBuildGiftInfo"() {
        given:
        AgreementGiftInfoType agreementGiftInfo = new AgreementGiftInfoType()
        agreementGiftInfo.setGiftToken("GIFT_TOKEN")
        agreementGiftInfo.setGiftDetailInfoList([new GiftDetailInfoType(giftType: "GIFT_TYPE", giftName: "GIFT_NAME")])
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(agreementGiftInfo: agreementGiftInfo, balanceType: "PP"))))
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();

        when:
        GiftInfo giftInfo = new MapperOfBookingInitResponse.BookingRoomInfoBuilder.PackageInfoBuilder().buildGiftInfo(checkAvailInfo)
        then:
        Assert.assertNotNull(giftInfo)
    }

    def "testNeedBuildArriveTimeInfo"() {
        when:
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(strategyInfos: [])
        Boolean hasHourRoomInfo = true
        ResourceToken resourceToken = null
        MapperOfBookingInitResponse.BookingRoomInfoBuilder builder = new MapperOfBookingInitResponse.BookingRoomInfoBuilder()
        then:
        Assert.assertFalse(builder.needBuildArriveTimeInfo(hasHourRoomInfo, resourceToken))

        when:
        bookingInitRequestType = new BookingInitRequestType(strategyInfos: [new StrategyInfo(strategyKey: "NEED_ARRIVE_TIME_INFO", strategyValue: "T")])
        builder = new MapperOfBookingInitResponse.BookingRoomInfoBuilder()
        then:
        Assert.assertFalse(builder.needBuildArriveTimeInfo(hasHourRoomInfo, resourceToken))
    }


    def "testConvertInvoiceTipToInvoiceTipToken"() {
        given:
        MapperOfBookingInitResponse.AdditionalInfoBuilder builder = new MapperOfBookingInitResponse.AdditionalInfoBuilder()

        when: "invoiceTip is null"
        def result1 = builder.convertInvoiceTipToInvoiceTipToken(null)

        then: "result should be null"
        result1 == null

        when: "invoiceTip has no details"
        InvoiceTip invoiceTipWithoutDetails = new InvoiceTip()
        invoiceTipWithoutDetails.setInvoiceTipType("TEST_TYPE")
        def result2 = builder.convertInvoiceTipToInvoiceTipToken(invoiceTipWithoutDetails)

        then: "result should have type but empty list"
        result2 != null
        result2.getInvoiceTipType() == "TEST_TYPE"
        result2.getInvoiceTipDetails() != null
        result2.getInvoiceTipDetails().size() == 0

        when: "invoiceTip has details"
        InvoiceTip invoiceTipWithDetails = new InvoiceTip()
        invoiceTipWithDetails.setInvoiceTipType("TEST_TYPE")

        InvoiceTipDetail detail1 = new InvoiceTipDetail()
        detail1.setType("DETAIL_TYPE_1")
        detail1.setCode("DETAIL_CODE_1")
        detail1.setDesc("DETAIL_DESC_1")

        InvoiceTipDetail detail2 = new InvoiceTipDetail()
        detail2.setType("DETAIL_TYPE_2")
        detail2.setCode("DETAIL_CODE_2")
        detail2.setDesc("DETAIL_DESC_2")

        invoiceTipWithDetails.setInvoiceTipDetails(Arrays.asList(detail1, detail2))
        def result3 = builder.convertInvoiceTipToInvoiceTipToken(invoiceTipWithDetails)

        then: "result should have type and converted details"
        result3 != null
        result3.getInvoiceTipType() == "TEST_TYPE"
        result3.getInvoiceTipDetails() != null
        result3.getInvoiceTipDetails().size() == 2

        result3.getInvoiceTipDetails().get(0).getType() == "DETAIL_TYPE_1"
        result3.getInvoiceTipDetails().get(0).getCode() == "DETAIL_CODE_1"
        result3.getInvoiceTipDetails().get(0).getDesc() == "DETAIL_DESC_1"

        result3.getInvoiceTipDetails().get(1).getType() == "DETAIL_TYPE_2"
        result3.getInvoiceTipDetails().get(1).getCode() == "DETAIL_CODE_2"
        result3.getInvoiceTipDetails().get(1).getDesc() == "DETAIL_DESC_2"
    }



    def "testBuildDefaultArrivalToken"() {
        when:
        ArriveTimeToken arriveTimeToken = new ArriveTimeToken()
        arriveTimeToken.setArriveTimeUTC("1")
        String ss = ProtobufSerializerUtil.pbSerialize(arriveTimeToken, ArriveTimeToken.class)
        MapperOfBookingInitResponse.BookingRoomInfoBuilder builder = new MapperOfBookingInitResponse.BookingRoomInfoBuilder()
        then:
        Assert.assertEquals(null, builder.buildDefaultArrivalToken(null))
        Assert.assertEquals(ss, builder.buildDefaultArrivalToken(ss))
    }

    def "testBuildPolicyServiceInfos"() {
        when:
        CheckAvailResponseType checkAvailResponseType = new CheckAvailResponseType
                (hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(balanceType: "PP")))
        checkAvailResponseType.getHotelRatePlan().roomDailyInfoList = Arrays.asList(
                new RoomDailyInfo(cNYAmount: new BigDecimal(486.66)),
                new RoomDailyInfo(cNYAmount: new BigDecimal(486.89)),
                new RoomDailyInfo(cNYAmount: new BigDecimal(486.89)))
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        MapperOfBookingInitResponse.BookingRoomInfoBuilder builder = new MapperOfBookingInitResponse.BookingRoomInfoBuilder()
        CancelPolicyDescType cancelPolicyDescType = new CancelPolicyDescType(descDetail: new CanceDescDetailType(paymentDesc: "123"))
        GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponseType = new GetSupportedPaymentMethodResponseType()
        getSupportedPaymentMethodResponseType.setGuaranteeMethodList(Arrays.asList(new GuaranteeMethodInfoType(guaranteeMethod: "ACCOUNT_GUARANTEE")))
        BookingInitAssembleRequest bookingInitAssembleRequest = BookingInitAssembleRequest.builder().withCheckAvailInfo(checkAvailInfo).withBookingInitRequest(new BookingInitRequestType(strategyInfos: new ArrayList<StrategyInfo>())).withGetSupportedPaymentMethodResponse(null).build()
        then:
        Assert.assertNotNull(builder.buildPolicyServiceInfos(new BookingRulesType(), null, bookingInitAssembleRequest))
        Assert.assertNotNull(builder.buildCancelPolicyAttr(cancelPolicyDescType, getSupportedPaymentMethodResponseType))
    }

    def "testBuildPayConfig"() {
        when:
        MapperOfBookingInitResponse.BookingConfigInfoBuilder builder = new MapperOfBookingInitResponse.BookingConfigInfoBuilder();
        PayConfigResponseType payConfigResponseType = new PayConfigResponseType();

        // Test case where payConfigResponseType is null
        then:
        Assert.assertNull(builder.buildPayConfig(null));

        when:
        payConfigResponseType.setPayMode("");
        then:
        Assert.assertNull(builder.buildPayConfig(payConfigResponseType));

        when:
        payConfigResponseType.setPayMode("OTHER_PAY_MODE");
        then:
        Assert.assertNotNull(builder.buildPayConfig(payConfigResponseType));

        when:
        payConfigResponseType.setPayMode("CustomerPayTriggerToAccnt");
        InitConfigInfo result = builder.buildPayConfig(payConfigResponseType);
        then:
        Assert.assertNotNull(result);
        Assert.assertEquals(InitConfigInfoConstant.CUSTOMER_PAY_TRIGGER_TO_ACCNT, result.getConfigKey());
        Assert.assertEquals("T", result.getConfigValue());
    }

    def "testBuildPayType"() {
        when:
        MapperOfFinishInfoResponse mapper = new MapperOfFinishInfoResponse()
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        CreateOrderResponseType createOrderResponseType = new CreateOrderResponseType()
        PaymentOrderCreateResponseType paymentOrderCreateResponseType = new PaymentOrderCreateResponseType()
        OrderCreateToken orderCreateToken = new OrderCreateToken()
        QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType = new QueryPaymentBillConfigResponseType()

        // Test case where SourceFrom is Offline
        orderCreateRequestType.setIntegrationSoaRequestType(new IntegrationSoaRequestType())
        orderCreateRequestType.getIntegrationSoaRequestType().setSourceFrom(SourceFrom.Offline)
        orderCreateRequestType.getIntegrationSoaRequestType().language = "zh-CN"
        then:
        Assert.assertEquals("NORMAL_PAY", mapper.buildPayType(orderCreateRequestType, createOrderResponseType, paymentOrderCreateResponseType, orderCreateToken, queryPaymentBillConfigResponseType))

        when:
        // Test case where SourceFrom is H5
        orderCreateRequestType.getIntegrationSoaRequestType().setSourceFrom(SourceFrom.H5)
        then:
        Assert.assertEquals("NORMAL_PAY", mapper.buildPayType(orderCreateRequestType, createOrderResponseType, paymentOrderCreateResponseType, orderCreateToken, queryPaymentBillConfigResponseType))

        when:
        // Test case where buildUnionPay returns true
        orderCreateRequestType.getIntegrationSoaRequestType().setSourceFrom(SourceFrom.H5)
        orderCreateRequestType.setHotelPayTypeInput(Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "UNION_PAY")))
        then:
        Assert.assertEquals("UNION_PAY", mapper.buildPayType(orderCreateRequestType, createOrderResponseType, paymentOrderCreateResponseType, orderCreateToken, queryPaymentBillConfigResponseType))

    }


    def "testBuildConfirmDetailInfoPriceChange"() {
        given:
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("currency", "CNY")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        CheckAvailResponseType checkAvailResponseType = new CheckAvailResponseType()
        checkAvailResponseType = JsonUtil.fromJson("{\"meta\":{\"exchangeRates\":[{\"sourceCurrency\":\"CNY\",\"targetCurrency\":\"CNY\",\"rate\":1}],\"token\":\"AUiA2/4\\u003d\"},\"wsId\":\"b80ecb60-d425-4950-b9f3-be5ecc40182f\",\"success\":true,\"hotelRatePlan\":{\"hotelInfo\":{\"hotelName\":{\"textGB\":\"艺龙瑞云酒店(楚雄昆百大楚悦广场店)\",\"textEn\":\"Elonog R.Y\"},\"masterHotelName\":\"艺龙瑞云酒店(楚雄昆百大楚悦广场店)\",\"masterHotelEnName\":\"Elonog R.Y\",\"hotelID\":*********,\"masterHotelId\":*********,\"star\":4,\"logoUrl\":\"http://dimg04.c-ctrip.com/images/0201e12000ej0o5kq091D_R.jpg\",\"customerEval\":0.0,\"address\":{\"textGB\":\"鹿城镇米市街社区灵秀路384号熙和名筑5栋\",\"textEn\":\"Building 5, Xihe Mingzhu, 384 Lingxiu Road, Mi Shi Street Community, Lucheng Town\"},\"hotelAddress\":\"鹿城镇米市街社区灵秀路384号熙和名筑5栋\",\"notifies\":[],\"hongKongMacaoTaiWan\":false,\"starLicence\":false,\"country\":1,\"province\":25,\"city\":3921,\"cityName\":{\"textGB\":\"楚雄市\",\"textEn\":\"Chuxiong\"},\"cityLocalName\":\"楚雄市\",\"localTimeOffsetHours\":0,\"hotelBrandInfo\":{\"groupId\":897,\"groupName\":\"艺龙酒店科技\",\"multiLanguageGroupName\":\"艺龙酒店科技\"},\"telephone\":\"+86-878-3160299\",\"hotelLogoUrl\":\"/0201e12000ej0o5kq091D.jpg\"},\"roomInfo\":{\"roomStaticInfo\":{\"bedInfoList\":[{\"parentBedTypeId\":360,\"parentBedTypeName\":\"大床\",\"childBedInfoList\":[{\"childBedTypeId\":4060,\"childBedTypeName\":\"大床\",\"bedCount\":1,\"bedWidth\":1.8,\"bedWidthRangeInfo\":{\"maxValue\":1.8,\"minValue\":1.51,\"unit\":\"m\",\"bedWidthDesc\":\"1.51-1.8米宽\"}}]}],\"windowInfo\":{\"windowTypeName\":\"有窗\",\"windowType\":2},\"complicatedBedInfoType\":{\"bedTypeDesc\":\"1张1.8米大床\",\"bedDetailDesc\":\"1张大床1.8米\",\"icon\":\"https://pages.c-ctrip.com/wireless-app/imgs/T2Images/0625/bedTitleIcon.png\",\"roomBedInfoList\":[{\"roomName\":\"卧室1\",\"roomType\":1,\"bedGroupList\":[{\"subBedInfoList\":[{\"bedName\":\"大床\",\"bedWidth\":1.8,\"bedCount\":1,\"bedIconUrl\":\"https://pages.c-ctrip.com/wireless-app/imgs/T2Images/0625/<EMAIL>\",\"bedDesc\":\"1张大床\",\"bedId\":4060,\"bedWidthRangeInfo\":{\"maxValue\":1.8,\"minValue\":1.51,\"unit\":\"m\",\"bedWidthDesc\":\"1.51-1.8米\"}}]}]}]}},\"priceItemInfo\":{\"originRoomAmount\":783.00,\"cNYRoomAmount\":783.00,\"customRoomAmount\":783.00,\"originFeeAmount\":0.00,\"cNYFeeAmount\":0.00,\"customFeeAmount\":0.00,\"totalOriginFeeAmount\":0,\"totalCNYFeeAmount\":0,\"totalCustomFeeAmount\":0,\"includeTaxFeeDesc\":\"\",\"excludeTaxFeeDesc\":\"\",\"totalOriginExtraPayTaxAmount\":0,\"totalCustomExtraPayTaxAmount\":0},\"taxDetails\":[],\"originAmount\":783,\"originCurrency\":\"CNY\",\"originExchange\":1,\"cNYAmount\":783,\"addBedFeeDesc\":\"加床价：该房型不可加床\",\"guestPerson\":2,\"minBookingRoomNum\":1,\"maxBookingRoomNum\":5,\"roomID\":1554984637,\"roomName\":{\"textGB\":\"艺宿雅居大床房（全屋智能+极米投影+零压床垫+记忆枕+干湿分离）\",\"textEn\":\"Yisu Elegant Residence Double Bed Room With Smart Home, Extreme Projector, Zero Pressure Mattress, And Wet-Dry Separation\"},\"subRoomName\":\"艺宿雅居大床房（全屋智能+极米投影+零压床垫+记忆枕+干湿分离）\",\"floor\":\"\",\"roomArea\":\"26-30\",\"bedType\":\"KingSize\",\"broadnet\":{\"textGB\":\"Wi-Fi免费 | 有线宽带免费\"},\"broadNetDesc\":\"Wi-Fi免费 | 有线宽带免费\",\"remarks\":[],\"receiveTextRemark\":false,\"specialTips\":[],\"description\":{\"textGB\":\"\",\"textEn\":\"\"},\"roomDescription\":\"\",\"roomDes\":[{\"textGB\":\"床型：大床\"},{\"textGB\":\"面积：26-30\"},{\"textGB\":\"加床价：该房型不可加床\"},{\"textGB\":\"有窗\"},{\"textGB\":\"网络：Wi-Fi免费 | 有线宽带免费\"}],\"roomSimpleDesInfo\":{\"bedType\":\"床型：大床\",\"roomArea\":\"面积：26-30\",\"smoke\":\"可吸烟\",\"broadNet\":\"Wi-Fi免费 | 有线宽带免费\"},\"otherDescription\":{},\"invoiceLimitType\":\"ByHotel\",\"availableVATInvoiceType\":\"Ordinary\",\"addPriceRoom\":false,\"roomPaymentRules\":{\"supportPaymentBill\":true,\"acceptCreditCard\":true,\"acceptExperience\":false,\"acceptTravelMoneyPay\":true},\"customAmount\":783,\"customCurrency\":\"CNY\",\"customExchange\":1,\"salePromotionInfo\":{\"prepayDiscountList\":[{\"avgAmount\":42,\"totalAmount\":126,\"typeConfigureID\":9,\"ruleConfigureID\":*********,\"avgCNYAmount\":42,\"totalCNYAmount\":126,\"customAmount\":42,\"totalCustomAmount\":126},{\"avgAmount\":40,\"totalAmount\":120,\"typeConfigureID\":109,\"ruleConfigureID\":*********,\"avgCNYAmount\":40,\"totalCNYAmount\":120,\"customAmount\":40,\"totalCustomAmount\":120},{\"avgAmount\":12.66,\"totalAmount\":38,\"typeConfigureID\":0,\"ruleConfigureID\":0,\"avgCNYAmount\":12.66,\"totalCNYAmount\":38,\"customAmount\":12.66,\"totalCustomAmount\":38}],\"promotionsDetailInfoList\":[{\"tagCode\":\"LZYH\",\"customTotalPromotionsPrice\":{\"price\":126,\"currency\":\"CNY\"},\"originTotalPromotionsPrice\":{\"price\":126.00,\"currency\":\"CNY\"}},{\"tagCode\":\"HJHY\",\"customTotalPromotionsPrice\":{\"price\":120,\"currency\":\"CNY\"},\"originTotalPromotionsPrice\":{\"price\":120.00,\"currency\":\"CNY\"}},{\"tagCode\":\"QWZD\",\"customTotalPromotionsPrice\":{\"price\":38,\"currency\":\"CNY\"},\"originTotalPromotionsPrice\":{\"price\":38.00,\"currency\":\"CNY\"}}],\"afterPromotOriginAmount\":499,\"afterPromotCNYAmount\":499,\"afterPromotCustomAmount\":499,\"promotOriginAmount\":284,\"promotCNYAmount\":284,\"promotCustomAmount\":284,\"bookRulePromtoList\":[]},\"saleRoomTags\":[{\"tagCode\":\"HJHY\",\"tagName\":\"黄金会员\",\"tagDesc\":\"\",\"configInfoList\":[]},{\"tagCode\":\"QWZD\",\"tagName\":\"商旅特惠\",\"tagDesc\":\"十亿豪补，每间夜立减13元\",\"configInfoList\":[]},{\"tagCode\":\"LZYH\",\"tagName\":\"连住优惠\",\"tagDesc\":\"多天连住优惠活动，仅限活动房型\",\"configInfoList\":[]}],\"applicativeCountryCodeList\":[],\"hourlyRoom\":false,\"hourRoomDetail\":{\"earliestArriveTime\":0,\"latestLeaveTime\":0,\"duration\":0,\"hourlyRoomTips\":\"钟点房 入住时间：\"},\"roomMealInfo\":{\"mealType\":4,\"roomMealDesc\":[\"2份早餐\"],\"dailyMealInfo\":[{\"dailyMealInfo\":[\"\"],\"effectDateUTC\":\"2024-12-16T16:00:00Z\",\"localEffectDate\":\"2024-12-17\"},{\"dailyMealInfo\":[\"2份早餐\"],\"effectDateUTC\":\"2024-12-17T16:00:00Z\",\"localEffectDate\":\"2024-12-18\"},{\"dailyMealInfo\":[\"2份早餐\"],\"effectDateUTC\":\"2024-12-18T16:00:00Z\",\"localEffectDate\":\"2024-12-19\"},{\"dailyMealInfo\":[\"2份早餐\"],\"effectDateUTC\":\"2024-12-19T16:00:00Z\",\"localEffectDate\":\"2024-12-20\"}]},\"roomCouponInfo\":{\"availableCoupon\":[],\"unAvailableCoupon\":[],\"multiCouponTotalCustomAmount\":0},\"roomAttributes\":{\"welfareRoom\":false,\"groupMemberShip\":false,\"premiumRoom\":false,\"generalizedPremiumRoom\":false},\"packageRoomInfo\":{\"packageRoom\":false,\"packageId\":0,\"xProductId\":[]},\"applicabilityId\":[],\"applicativeAreaPropertyValueId\":0,\"bonusPointInfo\":{\"bonusPointRoom\":false,\"tripLevelPointRoom\":true},\"lastCancelTimeUTC\":\"2024-12-17T10:00:00Z\",\"earlyArrivalTimeUTC\":\"2024-12-17T04:00:00Z\",\"lastArrivalTimeUTC\":\"2024-12-17T22:00:00Z\",\"masterBasicRoomId\":492272220,\"supportUserFilterBed\":false,\"localEarlyArrivalTime\":\"2024-12-17 12:00:00\",\"localLastArrivalTime\":\"2024-12-18 06:00:00\",\"balanceType\":\"PP\"},\"bookingRules\":{\"confirmRules\":{\"justifyConfirm\":true,\"confirmDuration\":0},\"billingGuestInfo\":{\"remarkLanguages\":[\"zh\",\"en\"],\"guestsNameLanguages\":[\"zh\",\"en\"]},\"limitDepartureDateTime\":{\"departureEnd\":\"2024-12-20T12:00:00+08\",\"departureEndUTC\":\"2024-12-20T04:00:00Z\"},\"certificateInfo\":{},\"cancelPolicyInfo\":{\"cancelType\":\"LADDER_FREE\",\"freeCancelPolicySceneType\":\"FREE_CANCEL_IN_30_MINUTES\",\"lastCancelTimeUTC\":\"2024-12-17T10:00:00Z\",\"guaranteePolicyInfo\":{\"guaranteeType\":\"FIRST_DAY_PREPAY\",\"paymentGuaranteePoly\":\"TRIP\",\"guaranteeReason\":\"NONE\",\"guaranteePriceInfo\":{\"originGuaranteePrice\":{\"price\":169,\"currency\":\"CNY\"},\"customGuaranteePrice\":{\"price\":169,\"currency\":\"CNY\"}},\"cancelDeductDetailInfo\":[{\"deductionStartTimeUTC\":\"2024-12-17T03:15:30Z\",\"deductionEndTimeUTC\":\"2024-12-17T10:00:00Z\",\"deductionRatio\":0.0,\"deductionType\":\"RADIO\",\"originDeductionPrice\":{\"price\":0,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":0,\"currency\":\"CNY\"}},{\"deductionStartTimeUTC\":\"2024-12-17T10:00:00Z\",\"deductionEndTimeUTC\":\"0001-12-29T16:00:00Z\",\"deductionRatio\":1,\"deductionType\":\"NIGHT\",\"originDeductionPrice\":{\"price\":169,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":169,\"currency\":\"CNY\"}}]},\"localLastCancelTime\":\"2024-12-17 18:00:00\"},\"addPriceRule\":{},\"acquirerInfoList\":[],\"minLOS\":1,\"roomStatus\":\"L\",\"roomQuantity\":5,\"nationalityRestrictionInfo\":{\"allowCountryCodeList\":[],\"blockCountryCodeList\":[]},\"restrictRuleList\":[]},\"roomDailyInfoList\":[{\"currency\":\"CNY\",\"exchange\":1,\"amount\":261.0,\"cNYCost\":229.68,\"cNYAmount\":261,\"customAmount\":261,\"holdRoomQuantity\":5,\"salePromotionInfo\":{\"prepayDiscountDetails\":[{\"amount\":42,\"ruleConfigureID\":*********,\"typeConfigureID\":9,\"surplusRooms\":-1,\"prepayCampaignID\":31,\"prepayCampaignName\":\"连住优惠\",\"cNYAmount\":42,\"customAmount\":42,\"discountDType\":1,\"tagID\":30},{\"amount\":40,\"ruleConfigureID\":*********,\"typeConfigureID\":109,\"surplusRooms\":-1,\"prepayCampaignID\":2695,\"prepayCampaignName\":\"国内EBK优享会\",\"cNYAmount\":40,\"customAmount\":40,\"discountDType\":1,\"tagID\":579},{\"amount\":10,\"ruleConfigureID\":0,\"typeConfigureID\":0,\"surplusRooms\":-1,\"prepayCampaignID\":774,\"prepayCampaignName\":\"平台主动补贴\",\"cNYAmount\":10,\"customAmount\":10,\"discountDType\":4,\"tagID\":300}],\"afterPromotOriginAmount\":169.0,\"afterPromotCNYAmount\":169,\"afterPromotCustomAmount\":169,\"promotOriginAmount\":92,\"promotCNYAmount\":92,\"promotCustomAmount\":92},\"meals\":2,\"effectDateUTC\":\"2024-12-16T16:00:00Z\",\"customAmountExcludeTax\":261.00,\"cNYAmountExcludeTax\":261.00,\"localEffectDate\":\"2024-12-17\"},{\"currency\":\"CNY\",\"exchange\":1,\"amount\":261.0,\"cNYCost\":229.68,\"cNYAmount\":261,\"customAmount\":261,\"holdRoomQuantity\":6,\"salePromotionInfo\":{\"prepayDiscountDetails\":[{\"amount\":42,\"ruleConfigureID\":*********,\"typeConfigureID\":9,\"surplusRooms\":-1,\"prepayCampaignID\":31,\"prepayCampaignName\":\"连住优惠\",\"cNYAmount\":42,\"customAmount\":42,\"discountDType\":1,\"tagID\":30},{\"amount\":40,\"ruleConfigureID\":*********,\"typeConfigureID\":109,\"surplusRooms\":-1,\"prepayCampaignID\":2695,\"prepayCampaignName\":\"国内EBK优享会\",\"cNYAmount\":40,\"customAmount\":40,\"discountDType\":1,\"tagID\":579},{\"amount\":14,\"ruleConfigureID\":0,\"typeConfigureID\":0,\"surplusRooms\":-1,\"prepayCampaignID\":774,\"prepayCampaignName\":\"平台主动补贴\",\"cNYAmount\":14,\"customAmount\":14,\"discountDType\":4,\"tagID\":300}],\"afterPromotOriginAmount\":165.0,\"afterPromotCNYAmount\":165,\"afterPromotCustomAmount\":165,\"promotOriginAmount\":96,\"promotCNYAmount\":96,\"promotCustomAmount\":96},\"meals\":2,\"effectDateUTC\":\"2024-12-17T16:00:00Z\",\"customAmountExcludeTax\":261.00,\"cNYAmountExcludeTax\":261.00,\"localEffectDate\":\"2024-12-18\"},{\"currency\":\"CNY\",\"exchange\":1,\"amount\":261.0,\"cNYCost\":229.68,\"cNYAmount\":261,\"customAmount\":261,\"holdRoomQuantity\":7,\"salePromotionInfo\":{\"prepayDiscountDetails\":[{\"amount\":42,\"ruleConfigureID\":*********,\"typeConfigureID\":9,\"surplusRooms\":-1,\"prepayCampaignID\":31,\"prepayCampaignName\":\"连住优惠\",\"cNYAmount\":42,\"customAmount\":42,\"discountDType\":1,\"tagID\":30},{\"amount\":40,\"ruleConfigureID\":*********,\"typeConfigureID\":109,\"surplusRooms\":-1,\"prepayCampaignID\":2695,\"prepayCampaignName\":\"国内EBK优享会\",\"cNYAmount\":40,\"customAmount\":40,\"discountDType\":1,\"tagID\":579},{\"amount\":14,\"ruleConfigureID\":0,\"typeConfigureID\":0,\"surplusRooms\":-1,\"prepayCampaignID\":774,\"prepayCampaignName\":\"平台主动补贴\",\"cNYAmount\":14,\"customAmount\":14,\"discountDType\":4,\"tagID\":300}],\"afterPromotOriginAmount\":165.0,\"afterPromotCNYAmount\":165,\"afterPromotCustomAmount\":165,\"promotOriginAmount\":96,\"promotCNYAmount\":96,\"promotCustomAmount\":96},\"meals\":2,\"effectDateUTC\":\"2024-12-18T16:00:00Z\",\"customAmountExcludeTax\":261.00,\"cNYAmountExcludeTax\":261.00,\"localEffectDate\":\"2024-12-19\"}],\"invoicePostPayTypeList\":[{\"postType\":\"RegularMail\",\"postPayType\":\"Free\",\"value\":0},{\"postType\":\"Ems\",\"postPayType\":\"Free\",\"value\":0},{\"postType\":\"Ems\",\"postPayType\":\"Charge\",\"value\":10},{\"postType\":\"Ems\",\"postPayType\":\"Integration\",\"value\":2000}],\"invoiceWhiteTypeList\":[]},\"reservationToken\":\"AQgDEgoyMDI0LTEyLTE3GhsKAU0SAlBQGgRUUklQMgNDTlk6ATFIvd285QUiAggAKg8KBXpoLUNOEgNDTlkaATE\\u003d\",\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-12-17 11:15:31.050+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}\n", CheckAvailResponseType.class)
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();

        BookingInitAssembleRequest request = BookingInitAssembleRequest.builder()
                .withBookInitToken(new BookInitToken(dayOfAvgPriceCustom: new BigDecimal(166.34)))
                .withCheckAvailInfo(checkAvailInfo)
                .withAccountInfo(accountInfo)
                .withResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken()))
                .build()

        when:
        com.ctrip.corp.bff.framework.hotel.entity.contract.ConfirmDetailInfo result = new MapperOfBookingInitResponse.ConfirmInfoBuilder().buildConfirmDetailInfoPriceChange(request)

        then:
        result != null
        result.getCode() == "PRICE_CHANGE"
        result.getConfirmDetailExtends().size() == 4
        result.getConfirmDetailExtends().get(0).key == "BEFORE_PRICE"
        result.getConfirmDetailExtends().get(0).value == "166.34"
        result.getConfirmDetailExtends().get(1).key == "AFTER_PRICE"
        result.getConfirmDetailExtends().get(1).value == "166.33"
        result.getConfirmDetailExtends().get(2).key == "BEFORE_CURRENCY"
        result.getConfirmDetailExtends().get(2).value == "CNY"
        result.getConfirmDetailExtends().get(3).key == "AFTER_CURRENCY"
        result.getConfirmDetailExtends().get(3).value == "CNY"
    }


    def "testBuildPriceChange"() {
        given:
        BookInitToken bookInitToken = new BookInitToken()
        bookInitToken.dayOfAvgPriceCny = dayOfAvgPriceCny
        CheckAvailResponseType checkAvailResponseType = new CheckAvailResponseType
                (hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(balanceType: "PP")))
        checkAvailResponseType.getHotelRatePlan().roomDailyInfoList = Arrays.asList(
                new RoomDailyInfo(cNYAmount: new BigDecimal(486.66)),
                new RoomDailyInfo(cNYAmount: new BigDecimal(486.89)),
                new RoomDailyInfo(cNYAmount: new BigDecimal(486.89)))
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();

        when:
        boolean priceChanged = new MapperOfBookingInitResponse.ConfirmInfoBuilder().buildPriceChange(bookInitToken, checkAvailInfo)

        then:
        priceChanged == expectedPriceChanged

        where:
        dayOfAvgPriceCny       || expectedPriceChanged
        new BigDecimal(486.82) || true
        new BigDecimal(486.81) || false
    }

    def "buildPromotionAmountInfo"() {
        expect:
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildPromotionAmountInfo(null, null) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildPromotionAmountInfo(new SalePromotionEntity(promotCustomAmount: 2), null) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildPromotionAmountInfo(new SalePromotionEntity(promotCustomAmount: 2), "USD").customAmountInfo.amount == "2"

    }

    def "buildSummaryPriceInfo"() {
        given:
        expect:
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildSummaryPriceInfo(null, null, null, null, null, null, null, null).promotionAmountInfo == null
    }

    def "testBuildBedWidth"() {
        when:
        CheckAvailResponseType checkAvailResponseType = new CheckAvailResponseType
                (hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(balanceType: "PP")))
        checkAvailResponseType.getHotelRatePlan().roomDailyInfoList = Arrays.asList(
                new RoomDailyInfo(cNYAmount: new BigDecimal(486.66)),
                new RoomDailyInfo(cNYAmount: new BigDecimal(486.89)),
                new RoomDailyInfo(cNYAmount: new BigDecimal(486.89)))
        MapperOfBookingInitResponse.BookingRoomInfoBuilder builder = new MapperOfBookingInitResponse.BookingRoomInfoBuilder()
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        then:
        Assert.assertNotNull(builder.buildBedWidth(new SubBedInfoType(bedWidth: 1.2f)))
        Assert.assertNotNull(builder.buildBedWidth(new SubBedInfoType(bedWidthRangeInfo: new BedWidthInfoType(maxValue: 1.8f, minValue: 1.5f))))
        Assert.assertNull(builder.getRoomAttributeValByType(RoomAttributeEnum.DURATION_OF_HOUR_ROOM, checkAvailInfo, new ResourceToken(), null, null))
    }

    def "testBuildServiceFeeCancelPolicyAttributeInfosForBook"() {
        when:
        List<ChargeAmountInfoType> chargeAmountInfoList = new ArrayList<>();
        ChargeAmountInfoType chargeAmountInfoType = new ChargeAmountInfoType();
        chargeAmountInfoList.add(chargeAmountInfoType);
        List<ServiceChargeInfoType> chargeItemDetailList = new ArrayList<>();
        chargeAmountInfoType.setChargeItemDetailList(chargeItemDetailList)
        chargeAmountInfoType.setChargeCategory("ModifyOrderServiceCharge")
        chargeAmountInfoType.setChargeMoment("PreCharge")
        ServiceChargeInfoType serviceChargeInfoType = new ServiceChargeInfoType()
        chargeItemDetailList.add(serviceChargeInfoType)
        serviceChargeInfoType.setChargeItemCode("ModifyOrderSC_WithinLMT")
        BaseChargeAmount chargeAmountPack = new BaseChargeAmount()
        serviceChargeInfoType.setChargeAmountPack(chargeAmountPack)
        chargeAmountPack.setChargeUnit("PerOrder")
        chargeAmountPack.setChargeAmountCustomCurrency(new ServiceChargePriceType(amount: new BigDecimal(100)))
        MapperOfBookingInitResponse.BookingRoomInfoBuilder builder = new MapperOfBookingInitResponse.BookingRoomInfoBuilder()
        GetRoomChargePolicyListResponseType getRoomChargePolicyListResponseType = new GetRoomChargePolicyListResponseType(chargeAmountInfoList: chargeAmountInfoList)
        CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType = new CalculateServiceChargeV2ResponseType(chargeAmountInfoList: chargeAmountInfoList)
        then:
        Assert.assertNotNull(builder.buildServiceFeeCancelPolicyAttributeInfosForBook(getRoomChargePolicyListResponseType, "2099-12-19T22:00:00Z", "CNY"))
        Assert.assertNotNull(builder.buildServiceFeeCancelPolicyAttributeInfosForModify(calculateServiceChargeV2ResponseType, "2099-12-19T22:00:00Z", "CNY"))
    }


    @Unroll
    def "testBuildCanAutoConfirmInitConfigInfo with different scenarios"() {
        given:
        def checkAvailResponseType = new CheckAvailResponseType(
                "hotelRatePlan": new HotelRatePlan(
                        "roomInfo": new RoomItem(balanceType: "PP", GDS: GDS),
                        "hotelInfo": new HotelItem(
                                "hotelBrandInfo": new HotelBrandItem(
                                        "groupId": 1234
                                )
                        )
                )
        )
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        BookingInitAssembleRequest bookInitAssembleRequest = new BookingInitAssembleRequest(
                bookingInitRequest: new BookingInitRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: sourceFrom)),
                resourceToken: new ResourceToken(
                        roomResourceToken: new RoomResourceToken(tmcPrice: tmcPrice, roomType: roomType)),
                checkAvailInfo: checkAvailInfo)

        when:
        InitConfigInfo result = new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildCanAutoConfirmInitConfigInfo(bookInitAssembleRequest)

        then:
        result.getConfigKey() == InitConfigInfoConstant.CAN_AUTO_CONFIRM
        result.getConfigValue() == expectedConfigValue

        where:
        sourceFrom         | GDS       | tmcPrice | roomType || expectedConfigValue
        SourceFrom.Offline | ""        | true     | "M"      || CommonConstant.OPEN
        SourceFrom.Offline | ""        | false    | "C"      || CommonConstant.OPEN
        SourceFrom.Offline | "Amadeus" | true     | "M"      || CommonConstant.OFF
        SourceFrom.Online  | ""        | true     | "M"      || CommonConstant.OFF
    }


    @Unroll
    def "testBuildCanSpecialAuthInitConfigInfo with different scenarios"() {
        given:
        def checkAvailResponseType = new CheckAvailResponseType(
                "hotelRatePlan": new HotelRatePlan(
                        "roomInfo": new RoomItem(balanceType: "PP"),
                        "hotelInfo": new HotelItem(
                                "hotelBrandInfo": new HotelBrandItem(
                                        "groupId": 1234
                                )
                        )
                )
        )
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        BookingInitAssembleRequest bookInitAssembleRequest = new BookingInitAssembleRequest(
                bookingInitRequest: new BookingInitRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: sourceFrom)),
                resourceToken: new ResourceToken(
                        roomResourceToken: new RoomResourceToken(roomType: "M")),
                checkAvailInfo: checkAvailInfo)
        when:
        InitConfigInfo result = new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildCanSpecialAuthInitConfigInfo(bookInitAssembleRequest)

        then:
        result.getConfigKey() == InitConfigInfoConstant.CAN_SPECIAL_AUTH
        result.getConfigValue() == expectedConfigValue

        where:
        sourceFrom         || expectedConfigValue
        SourceFrom.Online  || CommonConstant.OFF
        SourceFrom.Offline || CommonConstant.OFF
    }

    def testBuildAggCostAllocationToken() {
        when:
        def checkAvailResponseType = new CheckAvailResponseType(
                "hotelRatePlan": new HotelRatePlan(
                        "roomInfo": new RoomItem(balanceType: "PP"),
                        "hotelInfo": new HotelItem(
                                "hotelBrandInfo": new HotelBrandItem(
                                        "groupId": 1234
                                )
                        )
                )
        )
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("currency", "CNY")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("currency", "CNY")
                    }
                }))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        BookingInitAssembleRequest bookInitAssembleRequest = new BookingInitAssembleRequest(
                bookingInitRequest: new BookingInitRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.H5)),
                resourceToken: new ResourceToken(
                        roomResourceToken: new RoomResourceToken(roomType: "M")),
                checkAvailInfo: checkAvailInfo,
                accountInfo: accountInfo);
        MapperOfBookingInitResponse.CorpSpecificInfoBuilder builder = new MapperOfBookingInitResponse.CorpSpecificInfoBuilder()
        then:
        Assert.assertNotNull(builder.buildAggCostAllocationToken(bookInitAssembleRequest))
    }

    def "getCapacity with different scenarios"() {
        given:
        def checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        checkAvailInfo.getGuestPerson() >> guestPerson
        def bookingInitRequestType = new BookingInitRequestType(corpPayInfo: new CorpPayInfo("public"))
        def queryHotelOrderDataResponseType = new QueryHotelOrderDataResponseType()
        def roomNumberInfoConfig = new RoomNumberInfoConfig()
        def builder = new MapperOfBookingInitResponse.BookingConfigInfoBuilder()

        when:
        def result = builder.getCapacity(checkAvailInfo, bookingInitRequestType, queryHotelOrderDataResponseType, roomNumberInfoConfig)

        then:
        result == expectedCapacity

        where:
        guestPerson | maxPassengerNumber || expectedCapacity
        2           | 3                  || 2
        4           | 3                  || 4
        1           | 1                  || 1
        5           | 4                  || 5
        3           | 5                  || 3
    }

    def "getRoomAttributeValByType with different scenarios"() {
        given:
        def checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        checkAvailInfo.getRoomItem() >> new RoomItem(guestPerson: 2)
        def resourceToken = new ResourceToken()
        def roomNumberInfoConfig = new RoomNumberInfoConfig()
        def bookingInitRequestType = new BookingInitRequestType(corpPayInfo: new CorpPayInfo("public"))
        def builder = new MapperOfBookingInitResponse.BookingRoomInfoBuilder()

        when:
        def result = builder.getRoomAttributeValByType(roomAttributeEnum, checkAvailInfo, resourceToken, roomNumberInfoConfig, bookingInitRequestType)

        then:
        result == expectedValue

        where:
        roomAttributeEnum               || expectedValue
        RoomAttributeEnum.MAX_GUEST_NUM || "2"
        RoomAttributeEnum.MAX_GUEST_NUM || "2"
    }

    def "test buildRoomPropertiesForCertificateRoom when needCertification is true"() {
        given:
        // WrapperOfCheckAvail 初始化，注意 room,hotel,resourcetoken 不可为空
        CertificateInfoType certificateInfoType = new CertificateInfoType()
        certificateInfoType.setNeedCertificate(true)
        def mapperTest = new MapperOfBookingInitResponse()
        def checkAvailResponseType = new CheckAvailResponseType(
                "hotelRatePlan": new HotelRatePlan(
                        "bookingRules": new BookingRulesType(certificateInfo: certificateInfoType),
                        "roomInfo": new RoomItem(balanceType: "PP"),
                        "hotelInfo": new HotelItem(
                                "hotelBrandInfo": new HotelBrandItem(
                                        "groupId": 1234
                                )
                        )
                )
        )

        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken()))
                .build().getCheckAvailInfo()

        BookingInitAssembleRequest bookingInitAssembleRequest = new BookingInitAssembleRequest(checkAvailInfo: checkAvailInfo)

        when:
        List<RoomProperty> result = mapperTest.buildRoomPropertiesForCertificateRoom(bookingInitAssembleRequest)

        then:
        result.size() == 1
        result[0].getPropertyType() == RoomPropertyEnum.CERTIFICATE_ROOM.getPropertyType()
        result[0].getPropertyValue() == BooleanUtil.parseStr(true)
    }

    def "test buildDefaultRoomPropertyValueTrue"() {
        given:
        def mapperTest = new MapperOfBookingInitResponse()
        String propertyCode = RoomPropertyEnum.CERTIFICATE_ROOM.getPropertyType()

        when:
        RoomProperty result = mapperTest.buildDefaultRoomPropertyValueTrue(propertyCode)

        then:
        result.getPropertyType() == propertyCode
        result.getPropertyValue() == BooleanUtil.parseStr(true)
    }

    def "test buildRoomProperties when bookingInitAssembleRequest is null"() {
        when:
        def mapperTest = new MapperOfBookingInitResponse()
        List<RoomProperty> result = mapperTest.buildRoomProperties(null)

        then:
        result == null
    }

    def "test buildPolicyUid when bookingInitAssembleRequest is null"() {
        given:
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                policyInput: new PolicyInput(policyUid: policyUid),
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: userId)))
        expect:
        result == new MapperOfBookingInitResponse.AdditionalInfoBuilder().buildPolicyUid(bookingInitRequestType)

        where:
        policyUid | userId || result
        "123"     | "uid"  || "123"
        ""        | "uid"  || "uid"
    }

    @Unroll
    def "testGetCouponToken with #description"() {
        given: "Mock dependencies and inputs"
        def infoType = new CouponInfoType(platformCouponId: 3)

        when: "Calling getCouponToken method"
        def result = new MapperOfBookingInitResponse.AdditionalInfoBuilder().getCouponToken(infoType)

        then: "The result should match the expected outcome"
        result.platformCouponId == 3
    }

    @Unroll
    def "test buildControlResult"() {
        given:
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(balanceType: "PP"))))
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        BookingInitAssembleRequest request = BookingInitAssembleRequest.builder()
                .withBookInitToken(new BookInitToken(dayOfAvgPriceCustom: new BigDecimal(166.34)))
                .withRoomPayType(roomPayType)
                .withCheckTravelPolicyResponseType(checkTravelPolicyResponseType)
                .withResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken()))
                .withCheckAvailInfo(checkAvailInfo)
                .build()
        expect:
        result == new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildControlResult(request)

        where:
        checkTravelPolicyResponseType                                                                                                                                                                                                                                                                                                                                                                                                            | roomPayType               || result
        new CheckTravelPolicyResponseType(checkItemsResult: new CheckItemsResultType(checkControlResult: new CheckControlResultType(paymentType: "FORBID_BOOKING", inControl: false)))                                                                                                                                                                                                                                                           | HotelPayTypeEnum.SELF_PAY || "MEAL_STANDARD_FORBID_BOOKING"
        new CheckTravelPolicyResponseType(checkRcResult: new CheckRcResultType(checkResultList: Arrays.asList(new CheckResultType(checkResult: "FORBID_BOOKING"))))                                                                                                                                                                                                                                                                              | HotelPayTypeEnum.SELF_PAY || "FORBID_BOOKING"
        new CheckTravelPolicyResponseType(checkRcResult: new CheckRcResultType(overStandardRc: Arrays.asList(new CheckOverStandardRcInfoType(serviceChargeType: "NONE", required: true)), checkResultList: Arrays.asList(new CheckResultType(checkResult: "NEED_RC", serviceChargeType: "NONE"))))                                                                                                                                               | HotelPayTypeEnum.SELF_PAY || "NEED_RC"
        new CheckTravelPolicyResponseType(checkRcResult: new CheckRcResultType(overStandardRc: Arrays.asList(new CheckOverStandardRcInfoType(serviceChargeType: "CORP_PAY_SERVICE_CHARGE", required: true)), checkResultList: Arrays.asList(new CheckResultType(checkResult: "NEED_RC", serviceChargeType: "CORP_PAY_SERVICE_CHARGE"))))                                                                                                         | HotelPayTypeEnum.CORP_PAY || "NEED_CORP_RC"
        new CheckTravelPolicyResponseType(checkRcResult: new CheckRcResultType(overStandardRc: Arrays.asList(new CheckOverStandardRcInfoType(serviceChargeType: "PERSONAL_PAY_SERVICE_CHARGE", required: true), new CheckOverStandardRcInfoType(serviceChargeType: "CORP_PAY_SERVICE_CHARGE", required: false)), checkResultList: Arrays.asList(new CheckResultType(checkResult: "NEED_RC", serviceChargeType: "PERSONAL_PAY_SERVICE_CHARGE")))) | HotelPayTypeEnum.SELF_PAY || "NEED_CCARD_RC"
        new CheckTravelPolicyResponseType(checkRcResult: new CheckRcResultType(overStandardRc: Arrays.asList(new CheckOverStandardRcInfoType(serviceChargeType: "NONE", required: false)), checkResultList: Arrays.asList(new CheckResultType(checkResult: "VALID", serviceChargeType: "PERSONAL_PAY_SERVICE_CHARGE"))))                                                                                                                         | HotelPayTypeEnum.SELF_PAY || "IN_CONTROL"
    }

    @Unroll
    def "test buildSupportPersonalPayment"() {
        given:
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo)
        accountInfo.isOpenPersonalPayment(_, _, _) >> isOpenPersonalPayment
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(balanceType: balanceType))))
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        // AGG支付方式(INDIVIDUAL_PAY-个人支付, ACCOUNT_PAY-公帐支付, MIX_PAY-混付, CASH_PAY-前台现付, UNION_PAY-银联支付，CORPORATE_CARD_PAY-公务卡支付)
        BookingInitAssembleRequest request = BookingInitAssembleRequest.builder()
                .withBookInitToken(new BookInitToken(dayOfAvgPriceCustom: new BigDecimal(166.34)))
                .withGetSupportedPaymentMethodResponse(new GetSupportedPaymentMethodResponseType())
                .withResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken(balanceType: balanceType)))
                .withAccountInfo(accountInfo)
                .withCheckAvailInfo(checkAvailInfo)
                .withBookingInitRequest(new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public")))
                .build()
        expect:
        result == new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildSupportPersonalPayment(request)

        where:
        balanceType | isOpenPersonalPayment || result
        "PP"        | true                  || new InitConfigInfo(configKey: "SUPPORT_PERSONAL_PAYMENT", configValue: "T")
        "PP"        | false                 || new InitConfigInfo(configKey: "SUPPORT_PERSONAL_PAYMENT", configValue: "F")
        "FG"        | false                 || new InitConfigInfo(configKey: "SUPPORT_PERSONAL_PAYMENT", configValue: "T")
    }

    /*@Unroll
    def "test buildSupportCorpPayment"() {
        given:
        // AGG支付方式(INDIVIDUAL_PAY-个人支付, ACCOUNT_PAY-公帐支付, MIX_PAY-混付, CASH_PAY-前台现付, UNION_PAY-银联支付，CORPORATE_CARD_PAY-公务卡支付)
        BookingInitAssembleRequest request = BookingInitAssembleRequest.builder()
                .withBookInitToken(new BookInitToken(dayOfAvgPriceCustom: new BigDecimal(166.34)))
                .withGetSupportedPaymentMethodResponse(new GetSupportedPaymentMethodResponseType(paymentMethodList: paymentMethodList))
                .withResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken(balanceType: balanceType)))
                .build()
        expect:
        result == new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildSupportCorpPayment(request)

        where:
        balanceType | paymentMethodList                                                             || result
        "PP"        | Arrays.asList(new PaymentMethodInfoType(paymentMethod: "MIX_PAY"))            || new InitConfigInfo(configKey: "SUPPORT_CORP_PAYMENT", configValue: "T")
        "PP"        | Arrays.asList(new PaymentMethodInfoType(paymentMethod: "UNION_PAY"))          || new InitConfigInfo(configKey: "SUPPORT_CORP_PAYMENT", configValue: "F")
        "FG"        | Arrays.asList(new PaymentMethodInfoType(paymentMethod: "CASH_PAY"))           || new InitConfigInfo(configKey: "SUPPORT_CORP_PAYMENT", configValue: "F")
        "PP"        | Arrays.asList(new PaymentMethodInfoType(paymentMethod: "CORPORATE_CARD_PAY")) || new InitConfigInfo(configKey: "SUPPORT_CORP_PAYMENT", configValue: "F")
        "PP"        | Arrays.asList(new PaymentMethodInfoType(paymentMethod: "ACCOUNT_PAY"))        || new InitConfigInfo(configKey: "SUPPORT_CORP_PAYMENT", configValue: "T")
    }*/

    @Unroll
    def "test buildSupportCorpPayment"() {
        given:
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        checkAvailInfo.getHotelItem() >> new HotelItem(city: 1)
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo)
        accountInfo.supportCorpPay(_, _, _, _) >> isOpenCorpPayment
        BookingInitAssembleRequest request = BookingInitAssembleRequest.builder()
                .withCheckAvailInfo(checkAvailInfo)
                .withBookingInitRequest(new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public")))
                .withResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken(balanceType: balanceType)))
                .withAccountInfo(accountInfo)
                .build()
        expect:
        result == new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildSupportCorpPayment(request)

        where:
        balanceType | isOpenCorpPayment || result
        "PP"        | true              || new InitConfigInfo(configKey: "SUPPORT_CORP_PAYMENT", configValue: "T")
        "PP"        | false             || new InitConfigInfo(configKey: "SUPPORT_CORP_PAYMENT", configValue: "F")
        "FG"        | true              || new InitConfigInfo(configKey: "SUPPORT_CORP_PAYMENT", configValue: "F")
    }

    def testBuildInitConfigInfoShareRoom() {
        when:
        // AGG支付方式(INDIVIDUAL_PAY-个人支付, ACCOUNT_PAY-公帐支付, MIX_PAY-混付, CASH_PAY-前台现付, UNION_PAY-银联支付，CORPORATE_CARD_PAY-公务卡支付)
        BookingInitAssembleRequest request = BookingInitAssembleRequest.builder()
                .withBookInitToken(new BookInitToken(dayOfAvgPriceCustom: new BigDecimal(166.34)))
                .withGetSupportedPaymentMethodResponse(new GetSupportedPaymentMethodResponseType(paymentMethodList: null))
                .withResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken(balanceType: null)))
                .build()
        then:
        new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildInitConfigInfoShareRoom(new BookingInitRequestType(), null, new QueryHotelOrderDataResponseType()) != null
    }

    @Unroll
    def "buildRCContentBookAhead with different scenarios"() {
        given:
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        GetHotelTravelPolicyResponseType getHotelTravelPolicyResponseType = new GetHotelTravelPolicyResponseType(
                responseCode: 20000,
                finalPolicy: new FinalPolicyType(finalFloatingAmountSetting: new FinalFloatingAmountSettingType(floatAmountType: "TA", floatingAmount: new BigDecimal(10))));
        WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo hotelTravelPolicyInfo = WrapperOfHotelTravelPolicy.builder().hotelTravelPolicyResponseType(getHotelTravelPolicyResponseType).build();
        BookingInitAssembleRequest request = BookingInitAssembleRequest.builder()
                .withHotelTravelPolicyInfo(hotelTravelPolicyInfo)
                .withBookingInitRequest(new BookingInitRequestType(
                        integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001", userId: "_SL2240552768")),
                        corpPayInfo: new CorpPayInfo(corpPayType: "public")))
                .withBookInitToken(new BookInitToken(dayOfAvgPriceCustom: new BigDecimal(166.34)))
                .withCheckTravelPolicyResponseType(new CheckTravelPolicyResponseType(
                        checkRcResult: new CheckRcResultType(
                                overStandardRc: Arrays.asList(new CheckOverStandardRcInfoType(required: true, starInControl: true, priceInControl: false, serviceChargeType: "NONE")),
                                bookAheadRc: new CheckBookAheadRcInfoType(required: true, bookAheadDay: 5),
                                agreementRc: new CheckAgreementRcInfoType(required: true))))
                .withGetSupportedPaymentMethodResponse(new GetSupportedPaymentMethodResponseType(paymentMethodList: Arrays.asList(new PaymentMethodInfoType(paymentMethod: "MIX_PAY"))))
                .withResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken(balanceType: "PP")))
                .withGetReasoncodesResponseType(new GetReasoncodesResponseType(
                        reasonCodes: Arrays.asList(
                                new ReasoncodeInfo(reasonCode: "UY", reasonInfo: "提前一天", reasonInfoEn: "提前一天", rcType: 8),
                                new ReasoncodeInfo(reasonCode: "AC", reasonInfo: "中英文员工级别G级及以下,预订600到800协议酒店符合标准。", reasonInfoEn: "中英文员工级别G级及以下,预订600到800协议酒店符合标准。", rcType: 1),
                                new ReasoncodeInfo(reasonCode: "AO", reasonInfo: "中文协议房型满房", reasonInfoEn: "中文协议房型满房", rcType: 2))))
                .build()

        when:
        def result = new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildHotelRcInfos(request)
        then:
        result.get(0).rcType == "LOW_PRICE"
        result.get(0).rcDesc == "请选择未预订差标内房型的原因"
        result.get(0).rcTitle == "根据贵公司差旅政策，因您未预订符合价格房型，故请您选择原因（若继续预订，差补额度将加入差标，后续报销则相应扣减）"
        result.get(0).rcInfo.getRcContents().get(0).code == "AC"
        result.get(0).rcInfo.getRcContents().get(0).type == "LOW_PRICE"
        result.get(0).rcInfo.getRcContents().get(0).value == "中英文员工级别G级及以下,预订600到800协议酒店符合标准。"
        result.get(0).rcInfo.getRcContents().get(0).rcToken == "H4sIAAAAAAAA_wFhAJ7_CglMT1dfUFJJQ0USAkFDGk3kuK3oi7HmloflkZjlt6XnuqfliKtH57qn5Y-K5Lul5LiLLOmihOiuojYwMOWIsDgwMOWNj-iurumFkuW6l-espuWQiOagh-WHhuOAgiIBRoLzuaJhAAAA"
        result.get(1).rcType == "AGREEMENT"
        result.get(1).rcDesc == "请选择未预订协议房型的原因"
        result.get(1).rcTitle == "根据贵公司差旅政策，因您未预订协议房型，故请您选择原因"
        result.get(1).rcInfo.getRcContents().get(0).code == "AO"
        result.get(1).rcInfo.getRcContents().get(0).type == "AGREEMENT"
        result.get(1).rcInfo.getRcContents().get(0).value == "中文协议房型满房"
        result.get(1).rcInfo.getRcContents().get(0).rcToken == "H4sIAAAAAAAA_-PidHQPcnX1dfULEWJy9JeSeLJj7bNp7U97-1-sW_esY__Ted3Pdi8EMpQY3QCfmU6eLAAAAA"
        result.get(2).rcType == "BOOK_AHEAD"
        result.get(2).rcDesc == "未提前预订酒店的原因"
        result.get(2).rcTitle == "根据贵公司差旅政策，因您未提前5天预订，故请您选择原因"
        result.get(2).rcInfo.getRcContents().get(0).type == "BOOK_AHEAD"
        result.get(2).rcInfo.getRcContents().get(0).value == "提前一天"
        result.get(2).rcInfo.getRcContents().get(0).rcToken == "H4sIAAAAAAAA_-PicvL394539HB1dBFiCo2U4nnWP-FpZ--THQ1Pl6xUYnQDAKXFKZ4hAAAA"


        when: ""
        request = BookingInitAssembleRequest.builder()
                .withHotelTravelPolicyInfo(hotelTravelPolicyInfo)
                .withBookingInitRequest(new BookingInitRequestType(
                        integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001", userId: "_SL2240552768")),
                        corpPayInfo: new CorpPayInfo(corpPayType: "public")))
                .withBookInitToken(new BookInitToken(dayOfAvgPriceCustom: new BigDecimal(166.34)))
                .withCheckTravelPolicyResponseType(new CheckTravelPolicyResponseType(
                        checkRcResult: new CheckRcResultType(
                                overStandardRc: Arrays.asList(
                                        new CheckOverStandardRcInfoType(required: true, starInControl: true, priceInControl: false, serviceChargeType: "PERSONAL_PAY_SERVICE_CHARGE"),
                                        new CheckOverStandardRcInfoType(required: true, starInControl: true, priceInControl: true, serviceChargeType: "NONE")),
                                bookAheadRc: new CheckBookAheadRcInfoType(required: true, bookAheadDay: 5),
                                agreementRc: new CheckAgreementRcInfoType(required: true))))
                .withGetSupportedPaymentMethodResponse(new GetSupportedPaymentMethodResponseType(paymentMethodList: Arrays.asList(new PaymentMethodInfoType(paymentMethod: "MIX_PAY"))))
                .withResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken(balanceType: "PP")))
                .withGetReasoncodesResponseType(new GetReasoncodesResponseType(
                        reasonCodes: Arrays.asList(
                                new ReasoncodeInfo(reasonCode: "UY", reasonInfo: "提前一天", reasonInfoEn: "提前一天", rcType: 8),
                                new ReasoncodeInfo(reasonCode: "AC", reasonInfo: "中英文员工级别G级及以下,预订600到800协议酒店符合标准。", reasonInfoEn: "中英文员工级别G级及以下,预订600到800协议酒店符合标准。", rcType: 1),
                                new ReasoncodeInfo(reasonCode: "AO", reasonInfo: "中文协议房型满房", reasonInfoEn: "中文协议房型满房", rcType: 2))))
                .build()
        result = new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildHotelRcInfos(request)
        then:
        result.get(0).rcType == "LOW_PRICE"

        when: ""
        request = BookingInitAssembleRequest.builder()
                .withHotelTravelPolicyInfo(hotelTravelPolicyInfo)
                .withBookingInitRequest(new BookingInitRequestType(
                        integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001", userId: "_SL2240552768")),
                        corpPayInfo: new CorpPayInfo(corpPayType: "public")))
                .withBookInitToken(new BookInitToken(dayOfAvgPriceCustom: new BigDecimal(166.34)))
                .withCheckTravelPolicyResponseType(new CheckTravelPolicyResponseType(
                        checkRcResult: new CheckRcResultType(
                                overStandardRc: Arrays.asList(
                                        new CheckOverStandardRcInfoType(required: true, starInControl: true, priceInControl: false, serviceChargeType: "CORP_PAY_SERVICE_CHARGE"),
                                        new CheckOverStandardRcInfoType(required: true, starInControl: true, priceInControl: true, serviceChargeType: "NONE")),
                                bookAheadRc: new CheckBookAheadRcInfoType(required: true, bookAheadDay: 5),
                                agreementRc: new CheckAgreementRcInfoType(required: true))))
                .withGetSupportedPaymentMethodResponse(new GetSupportedPaymentMethodResponseType(paymentMethodList: Arrays.asList(new PaymentMethodInfoType(paymentMethod: "MIX_PAY"))))
                .withResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken(balanceType: "PP")))
                .withGetReasoncodesResponseType(new GetReasoncodesResponseType(
                        reasonCodes: Arrays.asList(
                                new ReasoncodeInfo(reasonCode: "UY", reasonInfo: "提前一天", reasonInfoEn: "提前一天", rcType: 8),
                                new ReasoncodeInfo(reasonCode: "AC", reasonInfo: "中英文员工级别G级及以下,预订600到800协议酒店符合标准。", reasonInfoEn: "中英文员工级别G级及以下,预订600到800协议酒店符合标准。", rcType: 1),
                                new ReasoncodeInfo(reasonCode: "AO", reasonInfo: "中文协议房型满房", reasonInfoEn: "中文协议房型满房", rcType: 2))))
                .build()
        result = new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildHotelRcInfos(request)
        then:
        result.get(0).rcType == "LOW_PRICE"
    }

    @Unroll
    def "buildOverStarCcard"() {
        given:
        CheckTravelPolicyResponseType request = new CheckTravelPolicyResponseType(
                checkRcResult: new CheckRcResultType(
                        overStandardRc: Arrays.asList(new CheckOverStandardRcInfoType(required: true, starInControl: starInControl, priceInControl: false, serviceChargeType: serviceChargeType)),
                        bookAheadRc: new CheckBookAheadRcInfoType(required: true, bookAheadDay: 5),
                        agreementRc: new CheckAgreementRcInfoType(required: true)))
        expect:
        result == new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildOverStarCcard(request)
        where:
        serviceChargeType             | starInControl || result
        "PERSONAL_PAY_SERVICE_CHARGE" | false         || true
        "PERSONAL_PAY_SERVICE_CHARGE" | true          || false
        "NONE"                        | false         || true
        "NONE"                        | true          || false
    }

    @Unroll
    def "buildOverPriceCcard"() {
        given:
        CheckTravelPolicyResponseType request = new CheckTravelPolicyResponseType(
                checkRcResult: new CheckRcResultType(
                        overStandardRc: Arrays.asList(new CheckOverStandardRcInfoType(required: true, starInControl: true, priceInControl: priceInControl, serviceChargeType: serviceChargeType)),
                        bookAheadRc: new CheckBookAheadRcInfoType(required: true, bookAheadDay: 5),
                        agreementRc: new CheckAgreementRcInfoType(required: true)))
        expect:
        result == new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildOverPriceCcard(request)
        where:
        serviceChargeType             | priceInControl || result
        "PERSONAL_PAY_SERVICE_CHARGE" | false          || true
        "PERSONAL_PAY_SERVICE_CHARGE" | true           || false
        "NONE"                        | false          || true
        "NONE"                        | true           || false
    }

    @Unroll
    def "buildOverStarCorp"() {
        given:
        CheckTravelPolicyResponseType request = new CheckTravelPolicyResponseType(
                checkRcResult: new CheckRcResultType(
                        overStandardRc: Arrays.asList(new CheckOverStandardRcInfoType(required: true, starInControl: starInControl, priceInControl: false, serviceChargeType: serviceChargeType)),
                        bookAheadRc: new CheckBookAheadRcInfoType(required: true, bookAheadDay: 5),
                        agreementRc: new CheckAgreementRcInfoType(required: true)))
        expect:
        result == new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildOverStarCorp(request)
        where:
        serviceChargeType         | starInControl || result
        "CORP_PAY_SERVICE_CHARGE" | false         || true
        "CORP_PAY_SERVICE_CHARGE" | true          || false
        "NONE"                    | false         || true
        "NONE"                    | true          || false
    }

    @Unroll
    def "buildOverPriceCorp"() {
        given:
        CheckTravelPolicyResponseType request = new CheckTravelPolicyResponseType(
                checkRcResult: new CheckRcResultType(
                        overStandardRc: Arrays.asList(new CheckOverStandardRcInfoType(required: true, starInControl: true, priceInControl: priceInControl, serviceChargeType: serviceChargeType)),
                        bookAheadRc: new CheckBookAheadRcInfoType(required: true, bookAheadDay: 5),
                        agreementRc: new CheckAgreementRcInfoType(required: true)))
        expect:
        result == new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildOverPriceCorp(request)
        where:
        serviceChargeType         | priceInControl || result
        "CORP_PAY_SERVICE_CHARGE" | false          || true
        "CORP_PAY_SERVICE_CHARGE" | true           || false
        "NONE"                    | false          || true
        "NONE"                    | true           || false
    }

    @Unroll
    def "emergencyBookChanged"() {
        given:
        expect:
        result == new MapperOfBookingInitResponse.BookingConfigInfoBuilder().emergencyBookChanged(approvalInput, bookInitToken)
        where:
        approvalInput                     | bookInitToken                         || result
        new ApprovalInput(emergency: "T") | new BookInitToken()                   || true
        new ApprovalInput(emergency: "T") | new BookInitToken(emergencyBook: "F") || true
        new ApprovalInput(emergency: "F") | new BookInitToken(emergencyBook: "T") || true
        null                              | new BookInitToken(emergencyBook: "T") || true
        new ApprovalInput(emergency: "T") | new BookInitToken(emergencyBook: "T") || false
        new ApprovalInput(emergency: "F") | new BookInitToken(emergencyBook: "F") || false
        new ApprovalInput(emergency: "F") | new BookInitToken(emergencyBook: "")  || false
        null                              | new BookInitToken(emergencyBook: "")  || false
    }

    def "buildFoodTitle"() {
        given:
        def tester = new MapperOfBookingInitResponse.BookingRoomInfoBuilder.PackageInfoBuilder()
        expect:
        tester.buildFoodTitle((null as XProductStaticInfoType), null) == ""
        tester.buildFoodTitle(new XProductStaticInfoType(), null) == ""
        tester.buildFoodTitle(new XProductStaticInfoType(productSpuInfoList: []), null) == ""
        tester.buildFoodTitle(new XProductStaticInfoType(productSpuInfoList: [null]), null) == ""
        tester.buildFoodTitle(new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType()]), null) == ""
        tester.buildFoodTitle(new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType(productSkuInfoList: [])]), null) == ""
        tester.buildFoodTitle(new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType(productSkuInfoList: [null])]), null) == ""
        tester.buildFoodTitle(new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType(productSkuInfoList: [new XProductSkuInfoType()])]), null) == ""
        tester.buildFoodTitle(new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType(productSkuInfoList: [new XProductSkuInfoType(category: new XProductCategoryInfoType())])]), null) == ""
        tester.buildFoodTitle(new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType(productSkuInfoList: [new XProductSkuInfoType(category: new XProductCategoryInfoType(categoryId: 201))])]), null) == ""
        tester.buildFoodTitle(new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType(productSkuInfoList: [new XProductSkuInfoType(category: new XProductCategoryInfoType(categoryId: 201), productName: "name")])]), null) == "name"
    }

    def "buildEnjoyTitle"() {
        given:
        def tester = new MapperOfBookingInitResponse.BookingRoomInfoBuilder.PackageInfoBuilder()
        expect:
        tester.buildEnjoyTitle((null as XProductStaticInfoType), null) == ""
        tester.buildEnjoyTitle(new XProductStaticInfoType(), null) == ""
        tester.buildEnjoyTitle(new XProductStaticInfoType(productSpuInfoList: []), null) == ""
        tester.buildEnjoyTitle(new XProductStaticInfoType(productSpuInfoList: [null]), null) == ""
        tester.buildEnjoyTitle(new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType()]), null) == ""
        tester.buildEnjoyTitle(new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType(productSkuInfoList: [])]), null) == ""
        tester.buildEnjoyTitle(new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType(productSkuInfoList: [null])]), null) == ""
        tester.buildEnjoyTitle(new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType(productSkuInfoList: [new XProductSkuInfoType()])]), null) == ""
        tester.buildEnjoyTitle(new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType(productSkuInfoList: [new XProductSkuInfoType(category: new XProductCategoryInfoType())])]), null) == ""
        tester.buildEnjoyTitle(new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType(productSkuInfoList: [new XProductSkuInfoType(category: new XProductCategoryInfoType(categoryId: 202))])]), null) == ""
        tester.buildEnjoyTitle(new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType(productSkuInfoList: [new XProductSkuInfoType(category: new XProductCategoryInfoType(categoryId: 202), productName: "name")])]), null) == "name"

    }

    def "isUseNewPackageInfo"() {
        given:
        def mapper = new MapperOfBookingInitResponse.BookingRoomInfoBuilder.PackageInfoBuilder()
        expect:
        !mapper.isUseNewPackageInfo(null)
        !mapper.isUseNewPackageInfo(new PackageExtendInfoType())
        !mapper.isUseNewPackageInfo(new PackageExtendInfoType())
        !mapper.isUseNewPackageInfo(new PackageExtendInfoType(["newXProduct": null]))
        !mapper.isUseNewPackageInfo(new PackageExtendInfoType(["newXProduct": "F"]))
        mapper.isUseNewPackageInfo(new PackageExtendInfoType(["newXProduct": "T"]))
    }


    @Unroll
    def "testMiceForbidInvoice with #description"() {
        given: "Mock BookingInitAssembleRequest"
        BookingInitAssembleRequest bookingInitAssembleRequest = BookingInitAssembleRequest.builder().withRoomPayType(roomPayType)
                .withBookingInitRequest(new BookingInitRequestType(miceInput: miceInput, integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: sourceFrom)))
                .build()


        and: "BookingInvoiceInfoBuilder instance"
        def builder = new MapperOfBookingInitResponse.BookingInvoiceInfoBuilder()

        when: "Calling miceForbidInvoice method"
        def result = builder.miceForbidInvoice(bookingInitAssembleRequest)

        then: "The result should match the expected outcome"
        result == expectedResult

        where: "Different scenarios for testing"
        description                   | miceInput                                    | sourceFrom         | roomPayType               || expectedResult
        "miceActivityId is not blank" | new MiceInput(miceActivityId: "activity123") | SourceFrom.Online  | HotelPayTypeEnum.SELF_PAY || true
        "miceActivityId is not blank" | new MiceInput(miceActivityId: "activity123") | SourceFrom.Offline | HotelPayTypeEnum.SELF_PAY || true
        "miceActivityId is not blank" | new MiceInput(miceActivityId: "activity123") | SourceFrom.H5      | HotelPayTypeEnum.SELF_PAY || false
        "miceActivityId is not blank" | new MiceInput(miceActivityId: "activity123") | SourceFrom.H5      | HotelPayTypeEnum.CORP_PAY || true
        "miceActivityId is blank"     | new MiceInput(miceActivityId: "")            | SourceFrom.H5      | HotelPayTypeEnum.SELF_PAY || false
        "miceActivityId is blank"     | new MiceInput()                              | SourceFrom.Offline | HotelPayTypeEnum.SELF_PAY || false
        "miceActivityId is blank"     | null                                         | SourceFrom.Offline | HotelPayTypeEnum.SELF_PAY || false
    }

    def "buildOnlinePayAmountInfo"() {
        expect:
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildOnlinePayAmountInfo(HotelPayTypeEnum.CASH, null, new GuaranteePriceType(customGuaranteePrice: new PriceType(price: 2, currency: "USD")), "USD", null, false, null, null) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildOnlinePayAmountInfo(HotelPayTypeEnum.CASH, null, new GuaranteePriceType(customGuaranteePrice: new PriceType(price: 2, currency: "USD")), "USD", null, false, HotelGuaranteeTypeEnum.SELF_GUARANTEE, null).customAmountInfo.amount == "2"
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildOnlinePayAmountInfo(HotelPayTypeEnum.CASH, null, new GuaranteePriceType(customGuaranteePrice: new PriceType(price: 2, currency: "USD")), "USD", null, false, HotelGuaranteeTypeEnum.CORP_GUARANTEE, null).customAmountInfo.amount == "2"
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildOnlinePayAmountInfo(HotelPayTypeEnum.CASH, new ChargeAmountInfoType(chargeAmountPack: new BaseChargeAmount(chargeAmountCustomCurrency: new ServiceChargePriceType(amount: 2))), null, "USD", null, false, null, HotelPayTypeEnum.CORP_PAY).customAmountInfo.amount == "2"
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildOnlinePayAmountInfo(HotelPayTypeEnum.CASH, new ChargeAmountInfoType(chargeAmountPack: new BaseChargeAmount(chargeAmountCustomCurrency: new ServiceChargePriceType(amount: 2))), null, "USD", null, false, null, HotelPayTypeEnum.SELF_PAY).customAmountInfo.amount == "2"
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildOnlinePayAmountInfo(HotelPayTypeEnum.CASH, new ChargeAmountInfoType(chargeAmountPack: new BaseChargeAmount(chargeAmountCustomCurrency: new ServiceChargePriceType(amount: 2))), null, "USD", null, false, null, null) == null

    }

    def "getPrimaryId"() {
        expect:
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().getPrimaryId(rep, uid) == res
        where:
        uid  | rep                                                                                                                                             || res
        null | null                                                                                                                                            || null
        "a"  | new QueryBizModeBindRelationResponseType()                                                                                                      || null
        "b"  | new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [])                                                                       || null
        "c"  | new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [null])                                                                   || null
        "d"  | new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData()])                                          || null
        "a"  | new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(primaryDimensionId: "2")])                   || null
        "a"  | new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "a", primaryDimensionId: "2")]) || "2"


    }


    @Unroll
    def "test buildConfirmDetailInfoHotelCheckAvailPriceChange with #description"() {
        given: "Mock dependencies and inputs"
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(customAmount: afterCustomAmount, balanceType: "PP", customCurrency: afterCustomCurrency))))
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        BookingInitAssembleRequest bookingInitAssembleRequest = BookingInitAssembleRequest.builder()
                .withCheckAvailInfo(checkAvailInfo)
                .withRoomPayType(HotelPayTypeEnum.CORP_PAY)
                .withResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken(customRoomAmount: customRoomAmount, customRoomAmountCurrency: customRoomAmountCurrency)))
                .withBookingInitRequest(new BookingInitRequestType(
                        integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.H5),
                        strategyInfos: Arrays.asList(new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "T")),
                        hotelBookInput: new HotelBookInput(roomQuantity: 1, adultQuantity: 1, hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2021-01-01", checkOut: "2021-01-02"))))
                .build()
        when: "Calling buildConfirmDetailInfoHotelCheckAvailPriceChange method"
        ConfirmDetailInfo result = new MapperOfBookingInitResponse.ConfirmInfoBuilder().buildConfirmDetailInfoHotelCheckAvailPriceChange(bookingInitAssembleRequest)

        then: "The result should match the expected outcome"
        result == expectedResult

        where: "Different scenarios for testing"
        description  | afterCustomAmount      | afterCustomCurrency | customRoomAmount       | customRoomAmountCurrency || expectedResult
        "scenario 1" | new BigDecimal(300)    | "CNY"               | new BigDecimal(200)    | "CNY"                    || new ConfirmDetailInfo(code: "PRICE_CHANGE_HOTEL_CHECK_AVAIL",
                confirmDetailExtends: [new ConfirmDetailExtend(key: "PRICE_CHANGE_TOTAL", value: "100"),
                                       new ConfirmDetailExtend(key: "PRICE_CHANGE_TOTAL_CURRENCY", value: "CNY"),
                                       new ConfirmDetailExtend(key: "PRICE_CHANGE_NIGHT", value: "100.0000000000"),
                                       new ConfirmDetailExtend(key: "PRICE_CHANGE_NIGHT_CURRENCY", value: "CNY"),
                                       new ConfirmDetailExtend(key: "PRICE_CHANGE_TYPE", value: "Up")])
        "scenario 1" | new BigDecimal(100)    | "CNY"               | new BigDecimal(300)    | "CNY"                    || new ConfirmDetailInfo(code: "PRICE_CHANGE_HOTEL_CHECK_AVAIL",
                confirmDetailExtends: [new ConfirmDetailExtend(key: "PRICE_CHANGE_TOTAL", value: "200"),
                                       new ConfirmDetailExtend(key: "PRICE_CHANGE_TOTAL_CURRENCY", value: "CNY"),
                                       new ConfirmDetailExtend(key: "PRICE_CHANGE_NIGHT", value: "200.0000000000"),
                                       new ConfirmDetailExtend(key: "PRICE_CHANGE_NIGHT_CURRENCY", value: "CNY"),
                                       new ConfirmDetailExtend(key: "PRICE_CHANGE_TYPE", value: "Down")])
        "scenario 1" | new BigDecimal(100)    | "CNY"               | new BigDecimal(300)    | "sgd"                    || null
        "scenario 1" | new BigDecimal(300)    | "CNY"               | new BigDecimal(300)    | "CNY"                    || null
        "scenario 1" | new BigDecimal(100.00) | "CNY"               | new BigDecimal(300.00) | "CNY"                    || new ConfirmDetailInfo(code: "PRICE_CHANGE_HOTEL_CHECK_AVAIL",
                confirmDetailExtends: [new ConfirmDetailExtend(key: "PRICE_CHANGE_TOTAL", value: "200"),
                                       new ConfirmDetailExtend(key: "PRICE_CHANGE_TOTAL_CURRENCY", value: "CNY"),
                                       new ConfirmDetailExtend(key: "PRICE_CHANGE_NIGHT", value: "200.0000000000"),
                                       new ConfirmDetailExtend(key: "PRICE_CHANGE_NIGHT_CURRENCY", value: "CNY"),
                                       new ConfirmDetailExtend(key: "PRICE_CHANGE_TYPE", value: "Down")])
    }


    @Unroll
    def "test isInvoiceTipChecked with #description"() {
        given:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                return true;
            }
        }
        MapperOfBookingInitResponse.BookingConfigInfoBuilder builder = new MapperOfBookingInitResponse.BookingConfigInfoBuilder()

        when:
        boolean result = builder.isInvoiceTipChecked(roomPayType, servicePayType, "")

        then:
        result == expectedResult

        where:
        description                    | roomPayType               | servicePayType            || expectedResult
        "both pay types are null"      | null                      | null                      || false
        "room pay type is null"        | null                      | HotelPayTypeEnum.CORP_PAY || false
        "service pay type is null"     | HotelPayTypeEnum.SELF_PAY | null                      || true
        "both pay types are SELF_PAY"  | HotelPayTypeEnum.SELF_PAY | HotelPayTypeEnum.SELF_PAY || true
        "room pay type is SELF_PAY"    | HotelPayTypeEnum.SELF_PAY | HotelPayTypeEnum.CORP_PAY || true
        "service pay type is SELF_PAY" | HotelPayTypeEnum.CORP_PAY | HotelPayTypeEnum.SELF_PAY || true
        "both pay types are CORP_PAY"  | HotelPayTypeEnum.CORP_PAY | HotelPayTypeEnum.CORP_PAY || false
    }

    @Unroll
    def "testBuildInvoiceTipChecked with #description"() {
        given:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                return true;
            }
        }
        MapperOfBookingInitResponse.BookingConfigInfoBuilder builder = new MapperOfBookingInitResponse.BookingConfigInfoBuilder()
        BookingInitAssembleRequest bookingInitAssembleRequest = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001"))))
                .withRoomPayType(roomPayType).build()

        when:
        InitConfigInfo result = builder.buildInvoiceTipChecked(bookingInitAssembleRequest)

        then:
        result == expectedResult

        where:
        description | roomPayType               || expectedResult
        "support"   | HotelPayTypeEnum.SELF_PAY || new InitConfigInfo(InitConfigInfoConstant.INVOICE_TIP_CHECKED, CommonConstant.OPEN)
        "unsupport" | HotelPayTypeEnum.CORP_PAY || new InitConfigInfo(InitConfigInfoConstant.INVOICE_TIP_CHECKED, CommonConstant.OFF)
    }

    @Unroll
    def "testBuildConfirmDetailInfoModifyServiceFee with #description"() {
        given:
        /*new MockUp<BookingInitUtil>() {
            @Mock
            public static ChargeAmountInfoType getChargeAmountInfoType(CalculateServiceChargeV2ResponseType responseType,
                                                                       HotelPayTypeEnum servicePayType, HotelPayTypeEnum roomPayType) {
                return new ChargeAmountInfoType(
                        chargeAmountPack: new BaseChargeAmount(chargeAmountCustomCurrency: new ServiceChargePriceType(amount: 2)),
                        chargeCategory: "ModifyOrderServiceCharge",
                        chargeItemDetailList: Arrays.asList(new ServiceChargeInfoType(
                                chargeCategory: "ModifyOrderServiceCharge",
                                chargeItemCode: "ModifyOrderSC_WithinLMT",
                                chargeAmountPack: new BaseChargeAmount(chargeAmountCustomCurrency: new ServiceChargePriceType(amount: 2)))))
            }
        }*/
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("currency", "CNY")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        def request = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType(strategyInfos: Arrays.asList(new StrategyInfo(strategyKey: "FIRST_REQUEST", strategyValue: "T"))))
                .withCalculateServiceChargeV2Response(new CalculateServiceChargeV2ResponseType(chargeAmountInfoList: Arrays.asList(new ChargeAmountInfoType(
                        paymentMethod: "AccountPay",
                        chargeMoment: "PreCharge",
                        chargeAmountPack: new BaseChargeAmount(chargeAmountCustomCurrency: new ServiceChargePriceType(amount: 2)),
                        chargeCategory: "ModifyOrderServiceCharge",
                        chargeItemDetailList: Arrays.asList(new ServiceChargeInfoType(
                                chargeCategory: "ModifyOrderServiceCharge",
                                chargeItemCode: "ModifyOrderSC_WithinLMT",
                                chargeAmountPack: new BaseChargeAmount(chargeAmountCustomCurrency: new ServiceChargePriceType(amount: 2))))))))
                .withRoomPayType(HotelPayTypeEnum.CORP_PAY)
                .withAccountInfo(accountInfo)
                .withServicePayType(HotelPayTypeEnum.CORP_PAY).build()
        request.getServicePayType() >> HotelPayTypeEnum.CORP_PAY
        request.getRoomPayType() >> HotelPayTypeEnum.CORP_PAY
        request.getCalculateServiceChargeV2ResponseType() >> new CalculateServiceChargeV2ResponseType()

        when:
        def result = new MapperOfBookingInitResponse.ConfirmInfoBuilder().buildConfirmDetailInfoModifyServiceFee(request)

        then:
        result != null
        result.getConfirmDetailExtends() == expectedValue

        where:
        description                  || expectedValue
        "valid request with value A" || Arrays.asList(new ConfirmDetailExtend(key: "SERVICE_FEE_CURRENCY", value: "CNY"), new ConfirmDetailExtend(key: "SERVICE_FEE_PRICE", value: "2"), new ConfirmDetailExtend(key: "CHARGE_ITEM_CODE", value: "ModifyOrderSC_WithinLMT"))
    }

    @Unroll
    def "test buildPersonAccountInvoiceTips"() {
        given:
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        new MockUp<PersonAccountUtil>() {
            @Mock
            public static String getPersonAccountName(QueryIndividualAccountResponseType queryIndividualAccountResponseType,
                                                      IntegrationSoaRequestType integrationSoaRequestType) {
                return "心程贝";
            }
        }
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001")),
                strategyInfos: new ArrayList<StrategyInfo>())
        bookingInitRequestType.getStrategyInfos().add(new StrategyInfo(strategyKey: "BOOKING_WITH_PERSONAL_ACCOUNT", strategyValue: "T"))
        BookingInitAssembleRequest bookingInitAssembleRequest = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(bookingInitRequestType)
                .build()
        when:
        InvoiceTip result = new MapperOfBookingInitResponse.InvoiceTipInfoBuilder().buildPersonAccountInvoiceTips(bookingInitAssembleRequest)

        then:
        result.invoiceTipType == "PERSON_ACCOUNT_PAY"
        result.invoiceTipDetails.get(0).type == "INVOICE_TIP"
        result.invoiceTipDetails.get(0).code == "PERSON_ACCOUNT_PAY"
        result.invoiceTipDetails.get(0).desc == "若使用心程贝支付，心程贝支付部分统一寄送到公司"
    }

    @Unroll
    def "test buildInsuranceFeeInvoiceTips"() {
        given:
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001")),
                hotelInsuranceInput: new HotelInsuranceInput(
                        hotelInsuranceDetailInputs: Arrays.asList(new HotelInsuranceDetailInput())))
        CorpOrderInvoiceDetailInfoQueryResponse getCorpOrderInvoiceDetailInfoQueryResponse = new CorpOrderInvoiceDetailInfoQueryResponse(
                hotelInvoiceDetailInfoList: Arrays.asList(new HotelInvoiceDetailInfo(
                        hotelFeeInfo: new HotelFeeInfo(feeType: "InsuranceFee", transactionFlag: "PERSONAL"),
                        travelSendType: travelSendType)))
        BookingInitAssembleRequest bookingInitAssembleRequest = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(bookingInitRequestType)
                .withCorpOrderInvoiceDetailInfoQueryResponse(getCorpOrderInvoiceDetailInfoQueryResponse)
                .withRoomPayType(roomPayType)
                .build()
        expect:
        result == new MapperOfBookingInitResponse.InvoiceTipInfoBuilder().buildInsuranceFeeInvoiceTips(bookingInitAssembleRequest)

        where:
        roomPayType               | travelSendType   || result
        HotelPayTypeEnum.CORP_PAY | "CorpSettlement" || new InvoiceTip(invoiceTipType: "INSURANCE", invoiceTipDetails: Arrays.asList(new InvoiceTipDetail(type: "INVOICE_TIP",
                code: "INSURANCE.CorpSettlement", desc: "开具普票并统一寄送到公司")))
        HotelPayTypeEnum.MIX_PAY  | "CorpSettlement" || new InvoiceTip(invoiceTipType: "INSURANCE", invoiceTipDetails: Arrays.asList(new InvoiceTipDetail(type: "INVOICE_TIP",
                code: "INSURANCE.CorpSettlement", desc: "开具普票并统一寄送到公司")))
        HotelPayTypeEnum.SELF_PAY | "CorpSettlement" || new InvoiceTip(invoiceTipType: "INSURANCE", invoiceTipDetails: Arrays.asList(new InvoiceTipDetail(type: "INVOICE_TIP",
                code: "INSURANCE.CorpSettlement", desc: "开具普票并统一寄送到公司")))
        HotelPayTypeEnum.SELF_PAY | "CorpOrder"      || new InvoiceTip(invoiceTipType: "INSURANCE", invoiceTipDetails: Arrays.asList(new InvoiceTipDetail(type: "INVOICE_TIP",
                code: "INSURANCE.CorpOrder", desc: "下单后在订单详情页申请开具普票")))
    }

    @Unroll
    def "test buildInsuranceFeeInvoiceTips-INVOICE"() {
        given:
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                if (key.equalsIgnoreCase("newInvoice")) {
                    return true
                }
                return false
            }
        }
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001")),
                hotelInsuranceInput: new HotelInsuranceInput(
                        hotelInsuranceDetailInputs: Arrays.asList(new HotelInsuranceDetailInput())))
        CorpOrderInvoiceDetailInfoQueryResponse getCorpOrderInvoiceDetailInfoQueryResponse = new CorpOrderInvoiceDetailInfoQueryResponse(
                hotelInvoiceDetailInfoList: Arrays.asList(new HotelInvoiceDetailInfo(
                        invoiceType: "INVOICE",
                        hotelFeeInfo: new HotelFeeInfo(feeType: "InsuranceFee", transactionFlag: "PERSONAL"),
                        travelSendType: travelSendType)))
        BookingInitAssembleRequest bookingInitAssembleRequest = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(bookingInitRequestType)
                .withCorpOrderInvoiceDetailInfoQueryResponse(getCorpOrderInvoiceDetailInfoQueryResponse)
                .withRoomPayType(roomPayType)
                .build()
        expect:
        result == new MapperOfBookingInitResponse.InvoiceTipInfoBuilder().buildInsuranceFeeInvoiceTips(bookingInitAssembleRequest)

        where:
        roomPayType               | travelSendType   || result
        HotelPayTypeEnum.CORP_PAY | "CorpSettlement" || new InvoiceTip(invoiceTipType: "INSURANCE", invoiceTipDetails: Arrays.asList(new InvoiceTipDetail(type: "INVOICE_TIP",
                code: "INSURANCE.INVOICE.CorpSettlement", desc: "消费凭证由供应商开具并统一寄送到公司")))
        HotelPayTypeEnum.MIX_PAY  | "CorpSettlement" || new InvoiceTip(invoiceTipType: "INSURANCE", invoiceTipDetails: Arrays.asList(new InvoiceTipDetail(type: "INVOICE_TIP",
                code: "INSURANCE.INVOICE.CorpSettlement", desc: "消费凭证由供应商开具并统一寄送到公司")))
        HotelPayTypeEnum.SELF_PAY | "CorpSettlement" || new InvoiceTip(invoiceTipType: "INSURANCE", invoiceTipDetails: Arrays.asList(new InvoiceTipDetail(type: "INVOICE_TIP",
                code: "INSURANCE.INVOICE.CorpSettlement", desc: "消费凭证由供应商开具并统一寄送到公司")))
        HotelPayTypeEnum.SELF_PAY | "CorpOrder"      || new InvoiceTip(invoiceTipType: "INSURANCE", invoiceTipDetails: Arrays.asList(new InvoiceTipDetail(type: "INVOICE_TIP",
                code: "INSURANCE.CorpOrder", desc: "下单后在订单详情页申请开具普票")))
    }

    @Unroll
    def "test buildServiceFeeInvoiceTips"() {
        given:
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001")),
                hotelInsuranceInput: new HotelInsuranceInput(
                        hotelInsuranceDetailInputs: Arrays.asList(new HotelInsuranceDetailInput())))
        CorpOrderInvoiceDetailInfoQueryResponse getCorpOrderInvoiceDetailInfoQueryResponse = new CorpOrderInvoiceDetailInfoQueryResponse(
                hotelInvoiceDetailInfoList: Arrays.asList(new HotelInvoiceDetailInfo(
                        hotelFeeInfo: new HotelFeeInfo(feeType: "ServiceFee", transactionFlag: "PERSONAL"),
                        travelSendType: travelSendType,
                        invoiceType: invoiceType,
                        invoiceCompany: invoiceCompany,
                        invoiceDetail: "VATAGENCYSERVICEFEE")))
        BookingInitAssembleRequest bookingInitAssembleRequest = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(bookingInitRequestType)
                .withCorpOrderInvoiceDetailInfoQueryResponse(getCorpOrderInvoiceDetailInfoQueryResponse)
                .withServicePayType(servicePayType)
                .build()
        expect:
        result == new MapperOfBookingInitResponse.InvoiceTipInfoBuilder().buildServiceFeeInvoiceTips(bookingInitAssembleRequest)

        where:
        servicePayType            | travelSendType   | invoiceType   | invoiceCompany || result
        HotelPayTypeEnum.NONE     | "CorpSettlement" | "DInvoice"    | "HONGRUI"      || null
        HotelPayTypeEnum.CORP_PAY | "CorpSettlement" | "DInvoice"    | "HONGRUI"      || new InvoiceTip(invoiceTipType: "SERVICE_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "SERVICE_FEE.CorpSettlement", desc: "携程预订商旅管理服务费发票会统一寄送到公司"),
                new InvoiceTipDetail(type: "BRIEF_INVOICE_TIP", code: "F")))
        HotelPayTypeEnum.SELF_PAY | "CorpSettlement" | "DInvoice"    | "HONGRUI"      || new InvoiceTip(invoiceTipType: "SERVICE_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "SERVICE_FEE.CorpSettlement", desc: "携程预订商旅管理服务费发票会统一寄送到公司"),
                new InvoiceTipDetail(type: "BRIEF_INVOICE_TIP", code: "F")))
        HotelPayTypeEnum.SELF_PAY | "CorpOrder"      | "DInvoice"    | "HONGRUI"      || new InvoiceTip(invoiceTipType: "SERVICE_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "SERVICE_FEE.CorpOrder", desc: "下单后在订单详情页申请开具发票，由上海携程宏睿国旅旅行社有限公司开具，可开具增值税普票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱地址，发票明细为经济代理*代理服务费"),
                new InvoiceTipDetail(type: "BRIEF_INVOICE_TIP", code: "T"),
                new InvoiceTipDetail(type: "SUPPORT_INVOICE_TYPE", code: "DInvoice", desc: "普票"),
                new InvoiceTipDetail(type: "INVOICE_COMPANY", code: "HONGRUI", desc: "上海携程宏睿国旅旅行社有限公司开具"),
                new InvoiceTipDetail(type: "INVOICE_DETAIL", code: "VATAGENCYSERVICEFEE", desc: "经济代理*代理服务费")))
        HotelPayTypeEnum.SELF_PAY | "CorpOrder"      | "DVatInvoice" | "NANTONG"      || new InvoiceTip(invoiceTipType: "SERVICE_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "SERVICE_FEE.CorpOrder.Vat", desc: "下单后在订单详情页申请开具发票，由上海携程宏睿国旅旅行社有限公司南通分公司开具，可开具增值税普票、专票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱地址，发票明细为经济代理*代理服务费"),
                new InvoiceTipDetail(type: "BRIEF_INVOICE_TIP", code: "T"),
                new InvoiceTipDetail(type: "SUPPORT_INVOICE_TYPE", code: "DVatInvoice", desc: "可专票"),
                new InvoiceTipDetail(type: "INVOICE_COMPANY", code: "NANTONG", desc: "上海携程宏睿国旅旅行社有限公司南通分公司开具"),
                new InvoiceTipDetail(type: "INVOICE_DETAIL", code: "VATAGENCYSERVICEFEE", desc: "经济代理*代理服务费")))
    }

    @Unroll
    def "test buildServiceFeeInvoiceTips-INVOICE"() {
        given:
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                if (key.equalsIgnoreCase("newInvoice")) {
                    return true
                }
                return false
            }
        }
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001")),
                hotelInsuranceInput: new HotelInsuranceInput(
                        hotelInsuranceDetailInputs: Arrays.asList(new HotelInsuranceDetailInput())))
        CorpOrderInvoiceDetailInfoQueryResponse getCorpOrderInvoiceDetailInfoQueryResponse = new CorpOrderInvoiceDetailInfoQueryResponse(
                hotelInvoiceDetailInfoList: Arrays.asList(new HotelInvoiceDetailInfo(
                        hotelFeeInfo: new HotelFeeInfo(feeType: "ServiceFee", transactionFlag: transactionFlag),
                        travelSendType: travelSendType,
                        invoiceType: invoiceType,
                        invoiceCompany: invoiceCompany,
                        invoiceDetail: "VATAGENCYSERVICEFEE")))
        BookingInitAssembleRequest bookingInitAssembleRequest = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(bookingInitRequestType)
                .withCorpOrderInvoiceDetailInfoQueryResponse(getCorpOrderInvoiceDetailInfoQueryResponse)
                .withServicePayType(servicePayType).withRoomPayType(roomPayType)
                .build()
        expect:
        result == new MapperOfBookingInitResponse.InvoiceTipInfoBuilder().buildServiceFeeInvoiceTips(bookingInitAssembleRequest)

        where:
        roomPayType               | servicePayType            | travelSendType   | invoiceType | invoiceCompany | transactionFlag || result
        HotelPayTypeEnum.CORP_PAY | HotelPayTypeEnum.CORP_PAY | "CorpSettlement" | "Invoice"   | "HONGRUI"      | "ACCNT"         || new InvoiceTip(invoiceTipType: "SERVICE_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "SERVICE_FEE.INVOICE.CorpSettlement", desc: "携程预订商旅管理服务费消费凭证会统一寄送到公司"),
                new InvoiceTipDetail(type: "SUPPORT_INVOICE_TYPE", code: "INVOICE", desc: "消费凭证")))
        HotelPayTypeEnum.SELF_PAY | HotelPayTypeEnum.SELF_PAY | "CorpSettlement" | "Invoice"   | "HONGRUI"      | "PERSONAL"      || new InvoiceTip(invoiceTipType: "SERVICE_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "SERVICE_FEE.INVOICE.CorpSettlement", desc: "携程预订商旅管理服务费消费凭证会统一寄送到公司"),
                new InvoiceTipDetail(type: "SUPPORT_INVOICE_TYPE", code: "INVOICE", desc: "消费凭证")))
        HotelPayTypeEnum.CASH     | HotelPayTypeEnum.SELF_PAY | "CorpOrder"      | "Invoice"   | "HONGRUI"      | "ACCNT"         || null
    }


    @Unroll
    def "test buildRoomFeeInvoiceTips-INVOICE"() {
        given:
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                if (key.equalsIgnoreCase("newInvoice")) {
                    return true
                }
                return false
            }
        }
        CorpOrderInvoiceDetailInfoQueryResponse getCorpOrderInvoiceDetailInfoQueryResponse = new CorpOrderInvoiceDetailInfoQueryResponse(
                hotelInvoiceDetailInfoList: Arrays.asList(new HotelInvoiceDetailInfo(
                        hotelFeeInfo: new HotelFeeInfo(feeType: "HotelFee", transactionFlag: transactionFlag),
                        travelSendType: travelSendType,
                        invoiceType: invoiceType,
                        invoiceCompany: invoiceCompany,
                        invoiceDetail: "BOOKINGACCOMMODATIONFEE")))
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001")),
                hotelInsuranceInput: new HotelInsuranceInput(
                        hotelInsuranceDetailInputs: Arrays.asList(new HotelInsuranceDetailInput())))
        BookingInitAssembleRequest bookingInitAssembleRequest = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(bookingInitRequestType)
                .withCorpOrderInvoiceDetailInfoQueryResponse(getCorpOrderInvoiceDetailInfoQueryResponse)
                .withRoomPayType(roomPayType)
                .build()
        expect:
        result == new MapperOfBookingInitResponse.InvoiceTipInfoBuilder().buildRoomFeeInvoiceTips(bookingInitAssembleRequest)

        where:
        roomPayType               | travelSendType   | invoiceType | invoiceCompany | transactionFlag || result
        HotelPayTypeEnum.CORP_PAY | "CorpSettlement" | "Invoice"   | "HONGRUI"      | "ACCNT"         || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.INVOICE.CorpSettlement", desc: "消费凭证由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司"),
                new InvoiceTipDetail(type: "SUPPORT_INVOICE_TYPE", code: "INVOICE", desc: "消费凭证")))
        HotelPayTypeEnum.SELF_PAY | "CorpSettlement" | "Invoice"   | "HONGRUI"      | "PERSONAL"      || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.INVOICE.CorpSettlement", desc: "消费凭证由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司"),
                new InvoiceTipDetail(type: "SUPPORT_INVOICE_TYPE", code: "INVOICE", desc: "消费凭证")))
    }

    @Unroll
    def "test buildRoomFeeInvoiceTips-INVOICE-MIX"() {
        given:
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                if (key.equalsIgnoreCase("newInvoice")) {
                    return true
                }
                return false
            }
        }
        CorpOrderInvoiceDetailInfoQueryResponse getCorpOrderInvoiceDetailInfoQueryResponse = new CorpOrderInvoiceDetailInfoQueryResponse(
                hotelInvoiceDetailInfoList: hotelInvoiceDetailInfoList)
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001")),
                hotelInsuranceInput: new HotelInsuranceInput(
                        hotelInsuranceDetailInputs: Arrays.asList(new HotelInsuranceDetailInput())))
        BookingInitAssembleRequest bookingInitAssembleRequest = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(bookingInitRequestType)
                .withCorpOrderInvoiceDetailInfoQueryResponse(getCorpOrderInvoiceDetailInfoQueryResponse)
                .withRoomPayType(roomPayType)
                .build()
        expect:
        result == new MapperOfBookingInitResponse.InvoiceTipInfoBuilder().buildRoomFeeInvoiceTips(bookingInitAssembleRequest)

        where:
        roomPayType              | hotelInvoiceDetailInfoList                                || result
        HotelPayTypeEnum.MIX_PAY | Arrays.asList(new HotelInvoiceDetailInfo(
                hotelFeeInfo: new HotelFeeInfo(feeType: "HotelFee", transactionFlag: "MIX"),
                travelSendType: "CorpSettlement",
                invoiceType: "Invoice",
                invoiceCompany: "HONGRUI",
                invoiceDetail: "BOOKINGACCOMMODATIONFEE"), new HotelInvoiceDetailInfo(
                hotelFeeInfo: new HotelFeeInfo(feeType: "HotelFee", transactionFlag: "MIX"),
                travelSendType: "CorpOrder",
                invoiceType: "Invoice",
                invoiceCompany: "HONGRUI",
                invoiceDetail: "BOOKINGACCOMMODATIONFEE"))                                   || null
        HotelPayTypeEnum.MIX_PAY | Arrays.asList(new HotelInvoiceDetailInfo(
                hotelFeeInfo: new HotelFeeInfo(feeType: "HotelFee", transactionFlag: "MIX"),
                travelSendType: "CorpSettlement",
                invoiceType: "Invoice",
                invoiceCompany: "HONGRUI",
                invoiceDetail: "BOOKINGACCOMMODATIONFEE"), new HotelInvoiceDetailInfo(
                hotelFeeInfo: new HotelFeeInfo(feeType: "HotelFee", transactionFlag: "MIX"),
                travelSendType: "CorpSettlement",
                invoiceType: "Invoice",
                invoiceCompany: "HONGRUI",
                invoiceDetail: "BOOKINGACCOMMODATIONFEE"))                                   || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.INVOICE.CorpSettlement", desc: "消费凭证由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司"),
                new InvoiceTipDetail(type: "SUPPORT_INVOICE_TYPE", code: "INVOICE", desc: "消费凭证")))
        HotelPayTypeEnum.MIX_PAY | Arrays.asList(new HotelInvoiceDetailInfo(
                hotelFeeInfo: new HotelFeeInfo(feeType: "HotelFee", transactionFlag: "MIX"),
                travelSendType: "CorpSettlement",
                invoiceType: "Invoice",
                invoiceCompany: "HONGRUI",
                invoiceDetail: "BOOKINGACCOMMODATIONFEE"), new HotelInvoiceDetailInfo(
                hotelFeeInfo: new HotelFeeInfo(feeType: "HotelFee", transactionFlag: "MIX"),
                travelSendType: "CorpOrder",
                invoiceType: "DInvoice",
                invoiceCompany: "HONGRUI",
                invoiceDetail: "BOOKINGACCOMMODATIONFEE"))                                   || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.MIX.INVOICE", desc: "个人支付部分下单后在订单详情页申请开具发票，由上海携程宏睿国旅旅行社有限公司开具，该房型可开具增值税普票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱，发票明细为经济代理*代订住宿费。公账支付部分的消费凭证会统一寄送到公司")))
        HotelPayTypeEnum.MIX_PAY | Arrays.asList(new HotelInvoiceDetailInfo(
                hotelFeeInfo: new HotelFeeInfo(feeType: "HotelFee", transactionFlag: "MIX"),
                travelSendType: "CorpSettlement",
                invoiceType: "Invoice",
                invoiceCompany: "HONGRUI",
                invoiceDetail: "BOOKINGACCOMMODATIONFEE"), new HotelInvoiceDetailInfo(
                hotelFeeInfo: new HotelFeeInfo(feeType: "HotelFee", transactionFlag: "MIX"),
                travelSendType: "CorpOrder",
                invoiceType: "DVatInvoice",
                invoiceCompany: "HONGRUI",
                invoiceDetail: "BOOKINGACCOMMODATIONFEE"))                                   || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.MIX.INVOICE.Vat", desc: "个人支付部分下单后在订单详情页申请开具发票，由上海携程宏睿国旅旅行社有限公司开具，该房型可开具增值税普票、专票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱，发票明细为经济代理*代订住宿费。公账支付部分的消费凭证会统一寄送到公司")))
    }

    @Unroll
    def "test buildRoomFeeInvoiceTips"() {
        given:
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        CorpOrderInvoiceDetailInfoQueryResponse getCorpOrderInvoiceDetailInfoQueryResponse = new CorpOrderInvoiceDetailInfoQueryResponse(
                hotelInvoiceDetailInfoList: Arrays.asList(new HotelInvoiceDetailInfo(
                        hotelFeeInfo: new HotelFeeInfo(feeType: "HotelFee", transactionFlag: transactionFlag),
                        travelSendType: travelSendType,
                        invoiceType: invoiceType,
                        invoiceCompany: invoiceCompany,
                        invoiceDetail: "BOOKINGACCOMMODATIONFEE")))
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001")),
                hotelInsuranceInput: new HotelInsuranceInput(
                        hotelInsuranceDetailInputs: Arrays.asList(new HotelInsuranceDetailInput())))
        BookingInitAssembleRequest bookingInitAssembleRequest = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(bookingInitRequestType)
                .withCorpOrderInvoiceDetailInfoQueryResponse(getCorpOrderInvoiceDetailInfoQueryResponse)
                .withRoomPayType(roomPayType)
                .build()
        expect:
        result == new MapperOfBookingInitResponse.InvoiceTipInfoBuilder().buildRoomFeeInvoiceTips(bookingInitAssembleRequest)

        where:
        roomPayType                         | travelSendType   | invoiceType   | invoiceCompany | transactionFlag || result
        HotelPayTypeEnum.CORP_PAY           | "CorpSettlement" | "DInvoice"    | "HONGRUI"      | "PERSONAL"      || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.CorpSettlement", desc: "发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司"),
                new InvoiceTipDetail(type: "BRIEF_INVOICE_TIP", code: "F")))
        HotelPayTypeEnum.FLASH_STAY_PAY     | "CorpSettlement" | "DInvoice"    | "HONGRUI"      | "PERSONAL"      || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.CorpSettlement", desc: "发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司"),
                new InvoiceTipDetail(type: "BRIEF_INVOICE_TIP", code: "F")))
        HotelPayTypeEnum.SELF_PAY           | "CorpSettlement" | "DInvoice"    | "HONGRUI"      | "PERSONAL"      || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.CorpSettlement", desc: "发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司"),
                new InvoiceTipDetail(type: "BRIEF_INVOICE_TIP", code: "F")))
        HotelPayTypeEnum.MIX_PAY            | "CorpSettlement" | "DInvoice"    | "HONGRUI"      | "MIX"           || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.CorpSettlement", desc: "发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司"),
                new InvoiceTipDetail(type: "BRIEF_INVOICE_TIP", code: "F")))
        HotelPayTypeEnum.SELF_PAY           | "CorpOrder"      | "DInvoice"    | "HONGRUI"      | "PERSONAL"      || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.CorpOrder", desc: "下单后在订单详情页申请开具发票，由上海携程宏睿国旅旅行社有限公司开具，该房型可开具增值税普票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱地址，发票明细为经济代理*代订住宿费"),
                new InvoiceTipDetail(type: "BRIEF_INVOICE_TIP", code: "T"),
                new InvoiceTipDetail(type: "SUPPORT_INVOICE_TYPE", code: "DInvoice", desc: "普票"),
                new InvoiceTipDetail(type: "INVOICE_COMPANY", code: "HONGRUI", desc: "上海携程宏睿国旅旅行社有限公司开具"),
                new InvoiceTipDetail(type: "INVOICE_DETAIL", code: "BOOKINGACCOMMODATIONFEE", desc: "经济代理*代订住宿费")))
        HotelPayTypeEnum.SELF_PAY           | "CorpOrder"      | "DVatInvoice" | "HUASHENG"     | "PERSONAL"      || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.CorpOrder.Vat", desc: "下单后在订单详情页申请开具发票，由西安华圣商旅服务有限公司开具，该房型可开具增值税普票、专票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱地址，发票明细为经济代理*代订住宿费"),
                new InvoiceTipDetail(type: "BRIEF_INVOICE_TIP", code: "T"),
                new InvoiceTipDetail(type: "SUPPORT_INVOICE_TYPE", code: "DVatInvoice", desc: "可专票"),
                new InvoiceTipDetail(type: "INVOICE_COMPANY", code: "HUASHENG", desc: "西安华圣商旅服务有限公司开具"),
                new InvoiceTipDetail(type: "INVOICE_DETAIL", code: "BOOKINGACCOMMODATIONFEE", desc: "经济代理*代订住宿费")))
        HotelPayTypeEnum.UNION_PAY          | "CorpOrder"      | "DVatInvoice" | "HUASHENG"     | "PERSONAL"      || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.CorpOrder.Vat", desc: "下单后在订单详情页申请开具发票，由西安华圣商旅服务有限公司开具，该房型可开具增值税普票、专票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱地址，发票明细为经济代理*代订住宿费"),
                new InvoiceTipDetail(type: "BRIEF_INVOICE_TIP", code: "T"),
                new InvoiceTipDetail(type: "SUPPORT_INVOICE_TYPE", code: "DVatInvoice", desc: "可专票"),
                new InvoiceTipDetail(type: "INVOICE_COMPANY", code: "HUASHENG", desc: "西安华圣商旅服务有限公司开具"),
                new InvoiceTipDetail(type: "INVOICE_DETAIL", code: "BOOKINGACCOMMODATIONFEE", desc: "经济代理*代订住宿费")))
        HotelPayTypeEnum.MIX_PAY            | "CorpOrder"      | "DInvoice"    | "NANTONG"      | "MIX"           || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.MIX", desc: "个人支付部分下单后在订单详情页申请开具发票，由上海携程宏睿国旅旅行社有限公司南通分公司开具，该房型可开具增值税普票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱，发票明细为经济代理*代订住宿费。公账支付部分发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司"),
                new InvoiceTipDetail(type: "BRIEF_INVOICE_TIP", code: "T"),
                new InvoiceTipDetail(type: "BRIEF_PAY_TYPE", code: "SELF_PAY", desc: "个付"),
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.CORP_PAY", desc: "公账支付部分发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司"),
                new InvoiceTipDetail(type: "SUPPORT_INVOICE_TYPE", code: "DInvoice", desc: "普票"),
                new InvoiceTipDetail(type: "INVOICE_COMPANY", code: "NANTONG", desc: "上海携程宏睿国旅旅行社有限公司南通分公司开具"),
                new InvoiceTipDetail(type: "INVOICE_DETAIL", code: "BOOKINGACCOMMODATIONFEE", desc: "经济代理*代订住宿费")))
        HotelPayTypeEnum.MIX_PAY            | "CorpOrder"      | "DVatInvoice" | "HONGRUI"      | "MIX"           || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.MIX.Vat", desc: "个人支付部分下单后在订单详情页申请开具发票，由上海携程宏睿国旅旅行社有限公司开具，该房型可开具增值税普票、专票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱，发票明细为经济代理*代订住宿费。公账支付部分发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司"),
                new InvoiceTipDetail(type: "BRIEF_INVOICE_TIP", code: "T"),
                new InvoiceTipDetail(type: "BRIEF_PAY_TYPE", code: "SELF_PAY", desc: "个付"),
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.CORP_PAY", desc: "公账支付部分发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司"),
                new InvoiceTipDetail(type: "SUPPORT_INVOICE_TYPE", code: "DVatInvoice", desc: "可专票"),
                new InvoiceTipDetail(type: "INVOICE_COMPANY", code: "HONGRUI", desc: "上海携程宏睿国旅旅行社有限公司开具"),
                new InvoiceTipDetail(type: "INVOICE_DETAIL", code: "BOOKINGACCOMMODATIONFEE", desc: "经济代理*代订住宿费")))
        HotelPayTypeEnum.MIX_PAY            | "CorpOrder"      | "DVatInvoice" | "HOTEL"        | "MIX"           || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.HOTEL.MIX", desc: "个人支付部分发票由酒店开具，请到前台索取。公账支付发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司"),
                new InvoiceTipDetail(type: "BRIEF_INVOICE_TIP", code: "F")))
        HotelPayTypeEnum.GUARANTEE_SELF_PAY | "CorpOrder"      | "DVatInvoice" | "HOTEL"        | "PERSONAL"      || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.HOTEL", desc: "发票由酒店开具，请到前台索取"),
                new InvoiceTipDetail(type: "BRIEF_INVOICE_TIP", code: "F")))
        HotelPayTypeEnum.CASH               | "CorpOrder"      | "DVatInvoice" | "HOTEL"        | "PERSONAL"      || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.HOTEL", desc: "发票由酒店开具，请到前台索取"),
                new InvoiceTipDetail(type: "BRIEF_INVOICE_TIP", code: "F")))
        HotelPayTypeEnum.GUARANTEE_CORP_PAY | "CorpOrder"      | "DVatInvoice" | "HOTEL"        | "PERSONAL"      || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(
                new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.HOTEL", desc: "发票由酒店开具，请到前台索取"),
                new InvoiceTipDetail(type: "BRIEF_INVOICE_TIP", code: "F")))
    }

    @Unroll
    def "test buildRoomFeeInvoiceTips default"() {
        given:
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001")),
                hotelInsuranceInput: new HotelInsuranceInput(
                        hotelInsuranceDetailInputs: Arrays.asList(new HotelInsuranceDetailInput())))
        BookingInitAssembleRequest bookingInitAssembleRequest = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(bookingInitRequestType)
                .withCorpOrderInvoiceDetailInfoQueryResponse(null)
                .withRoomPayType(roomPayType)
                .build()
        expect:
        result == new MapperOfBookingInitResponse.InvoiceTipInfoBuilder().buildRoomFeeInvoiceTips(bookingInitAssembleRequest)

        where:
        roomPayType               || result
        HotelPayTypeEnum.CORP_PAY || new InvoiceTip(invoiceTipType: "ROOM_FEE", invoiceTipDetails: Arrays.asList(new InvoiceTipDetail(type: "INVOICE_TIP", code: "ROOM_FEE.DEFAULT", desc: "下单后在订单详情页发票信息中查看开票信息")))
    }

    @Unroll
    def "testBuildSupportMiniAppPayNew with #description"() {
        given: "A BookingConfigInfoBuilder instance and a mocked BookingInitAssembleRequest"
        MapperOfBookingInitResponse.BookingConfigInfoBuilder builder = new MapperOfBookingInitResponse.BookingConfigInfoBuilder()
        BookingInitAssembleRequest request = Mock(BookingInitAssembleRequest) {
            getCheckAvailInfo() >> Mock(WrapperOfCheckAvail.CheckAvailInfo) {
                getHotelBalanceTypeEnum() >> hotelBalanceTypeEnum
                getPaymentGuaranteePolyEnum() >> paymentGuaranteePoly
                getOriginCurrency() >> originCurrency
                getCustomCurrency() >> customCurrency
                getBookingRules() >> Mock(BookingRulesType) {
                    getCancelPolicyInfo() >> Mock(CancelPolicyType) {
                        getCancelType() >> cancelType
                    }
                }
                isNeedGuarantee() >> needGuarantee
            }
            getServicePayType() >> servicePayType
        }

        when: "Calling buildSupportMiniAppPayNew method"
        boolean result = builder.buildSupportMiniAppPayNew(request)

        then: "The result should match the expected outcome"
        result == expectedResult

        where: "Different scenarios for testing"
        description                   | hotelBalanceTypeEnum       | servicePayType            | paymentGuaranteePoly                  | originCurrency | customCurrency | cancelType |needGuarantee|| expectedResult
        "PP cny"                      | HotelBalanceTypeEnum.PP    | null                      | null                                  | "USD"          | "CNY"          | "FREE"     |false|| true
        "USEFG cny"                   | HotelBalanceTypeEnum.USEFG | null                      | null                                  | "USD"          | "CNY"          | "FREE"     |false|| true
        "PP usd"                      | HotelBalanceTypeEnum.PP    | null                      | null                                  | "CNY"          | "USD"          | "FREE"     |false|| false
        "USEFG usd"                   | HotelBalanceTypeEnum.USEFG | null                      | null                                  | "CNY"          | "USD"          | "FREE"     |false|| false
        "fg guarantee toCtrip cny"    | HotelBalanceTypeEnum.FG    | null                      | PaymentGuaranteePolyEnum.PAY_TO_CTRIP | "USD"          | "CNY"          | "NOFREE"   |true|| true
        "fg guarantee toCtrip usd"    | HotelBalanceTypeEnum.FG    | null                      | PaymentGuaranteePolyEnum.PAY_TO_CTRIP | "CNY"          | "USD"          | "NOFREE"   |true|| false
        "fg toHotel cny"              | HotelBalanceTypeEnum.FG    | null                      | PaymentGuaranteePolyEnum.PAY_TO_HOTEL | "CNY"          | "USD"          | "NOFREE"   |true|| true
        "fg toHotel usd"              | HotelBalanceTypeEnum.FG    | null                      | PaymentGuaranteePolyEnum.PAY_TO_HOTEL | "USD"          | "CNY"          | "NOFREE"   |true|| false
        "cash and serviceFee is self" | HotelBalanceTypeEnum.FG    | HotelPayTypeEnum.SELF_PAY | null                                  | "USD"          | "CNY"          | "FREE"     |false|| true
        "cash and serviceFee is corp" | HotelBalanceTypeEnum.FG    | HotelPayTypeEnum.CORP_PAY | null                                  | "USD"          | "CNY"          | "FREE"     |false|| false
        "cash"                        | HotelBalanceTypeEnum.FG    | null                      | null                                  | "USD"          | "CNY"          | "FREE"     |false|| false
    }

    @Unroll
    def "buildPassengerSmsConfirmDefaultChecked with #corpIDTest"() {
        given:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                if (!StringUtils.isEmpty(corpId)) {
                    return true;
                }

                return false;
            }
        }
        MapperOfBookingInitResponse.BookingConfigInfoBuilder builder = new MapperOfBookingInitResponse.BookingConfigInfoBuilder()
        BookingInitAssembleRequest bookingInitAssembleRequest = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: corpIDTest))))
                .build()

        when:
        InitConfigInfo result = builder.buildPassengerSmsConfirmDefaultChecked(bookingInitAssembleRequest)

        then:
        result == expectedResult

        where:
        corpIDTest || expectedResult
        "support"  || new InitConfigInfo(InitConfigInfoConstant.PASSENGER_SMS_CONFIRM_DEFAULT_CHECKED, CommonConstant.OPEN)
        ""         || new InitConfigInfo(InitConfigInfoConstant.PASSENGER_SMS_CONFIRM_DEFAULT_CHECKED, CommonConstant.OFF)
    }

    @Unroll
    def "test buildBookResourceToken"() {
        given:
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001")))
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailInfo) {
            getHotelBalanceTypeEnum() >> hotelBalanceTypeEnum
        }
        BookingInitAssembleRequest bookingInitAssembleRequest = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(bookingInitRequestType)
                .withCorpOrderInvoiceDetailInfoQueryResponse(null)
                .withRoomPayType(roomPayType)
                .withServicePayType(servicePayType)
                .withCheckAvailInfo(checkAvailInfo)
                .withGetSupportedPaymentMethodResponse(new GetSupportedPaymentMethodResponseType(guaranteeMethodList: guaranteeMethodList))
                .build()
        expect:
        def result = new MapperOfBookingInitResponse.HotelResourceTokenBuilder().buildBookResourceToken(bookingInitAssembleRequest)
        with(result) {
            bookInfoStr == result.bookInfoStr
            bookVersion == result.bookVersion
        }

        where:
        roomPayType                     | servicePayType            | hotelBalanceTypeEnum    | guaranteeMethodList                                                                                                                                    || bookVersion | bookInfoStr
        HotelPayTypeEnum.CORP_PAY       | HotelPayTypeEnum.CORP_PAY | HotelBalanceTypeEnum.PP | null                                                                                                                                                   || "2"         | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "CORP_PAY", servicePayType: "CORP_PAY"))
        HotelPayTypeEnum.SELF_PAY       | HotelPayTypeEnum.SELF_PAY | HotelBalanceTypeEnum.PP | null                                                                                                                                                   || "2"         | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "SELF_PAY", servicePayType: "SELF_PAY"))
        HotelPayTypeEnum.FLASH_STAY_PAY | HotelPayTypeEnum.CORP_PAY | HotelBalanceTypeEnum.PP | null                                                                                                                                                   || "2"         | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "FLASH_STAY_PAY", servicePayType: "CORP_PAY"))
        HotelPayTypeEnum.UNION_PAY      | HotelPayTypeEnum.SELF_PAY | HotelBalanceTypeEnum.PP | null                                                                                                                                                   || "2"         | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "UNION_PAY", servicePayType: "SELF_PAY"))
        HotelPayTypeEnum.CORP_PAY       | HotelPayTypeEnum.CORP_PAY | HotelBalanceTypeEnum.PP | null                                                                                                                                                   || "2"         | "cd3dc0e9b4538eedec8b37f974469b25"
        HotelPayTypeEnum.CASH           | HotelPayTypeEnum.CORP_PAY | HotelBalanceTypeEnum.FG | Arrays.asList()                                                                                                                                        || "2"         | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "CASH", servicePayType: "CORP_PAY"))
        HotelPayTypeEnum.CASH           | HotelPayTypeEnum.SELF_PAY | HotelBalanceTypeEnum.FG | null                                                                                                                                                   || "2"         | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "CASH", servicePayType: "SELF_PAY"))
        HotelPayTypeEnum.CASH           | HotelPayTypeEnum.CORP_PAY | HotelBalanceTypeEnum.FG | Arrays.asList(new GuaranteeMethodInfoType(guaranteeMethod: "ACCOUNT_GUARANTEE"), new GuaranteeMethodInfoType(guaranteeMethod: "INDIVIDUAL_GUARANTEE")) || "2"         | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "GUARANTEE_CORP_PAY", servicePayType: "CORP_PAY"))
        HotelPayTypeEnum.CASH           | HotelPayTypeEnum.SELF_PAY | HotelBalanceTypeEnum.FG | Arrays.asList(new GuaranteeMethodInfoType(guaranteeMethod: "INDIVIDUAL_GUARANTEE"))                                                                    || "2"         | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "GUARANTEE_SELF_PAY", servicePayType: "SELF_PAY"))
    }


    @Unroll
    def "testBuildArriveTimeKey with #description"() {
        given: "A BookingInitResponseType instance"
        def response = new BookingInitResponseType(
                bookingRoomInfo: new BookingRoomInfo(arriveTimeInfo: arriveTimeInfo))

        when: "Calling buildArriveTimeKey method"
        def result = new MapperOfBookingInitResponse.HotelResourceTokenBuilder().buildArriveTimeKey(response)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                | arriveTimeInfo                                                                                                                                                                                         | expectedResult
        "arriveTimeInfo is null"   | null                                                                                                                                                                                                   | null
        "arriveTimeInfo has value" | new ArriveTimeInfo(arriveTimeDetailInfos: Arrays.asList(new ArriveTimeDetailInfo(arriveTimeToken: "15 free", checked: "F"), new ArriveTimeDetailInfo(arriveTimeToken: "16:00 no free", checked: "T"))) | "16:00 no free"
        "arriveTimeInfo has value" | new ArriveTimeInfo(arriveTimeDetailInfos: Arrays.asList(new ArriveTimeDetailInfo(arriveTimeToken: "15 free", checked: "T"), new ArriveTimeDetailInfo(arriveTimeToken: "16:00 no free", checked: "F"))) | "15 free"
        "arriveTimeInfo has value" | new ArriveTimeInfo(arriveTimeDetailInfos: Arrays.asList(new ArriveTimeDetailInfo(arriveTimeToken: "15 free", checked: "T"), new ArriveTimeDetailInfo()))                                               | "15 free"
        "arriveTimeInfo has value" | new ArriveTimeInfo(arriveTimeDetailInfos: null)                                                                                                                                                        | null
    }


    @Unroll
    def "testGetRoomInvoiceContainerTypes with #description"() {
        given: "A set of input parameters"
        when: "Calling getRoomInvoiceContainerTypes method"
        def result = new MapperOfBookingInitResponse.BookingInvoiceInfoBuilder().getRoomInvoiceContainerTypes(
                new GetCorpUserInfoResponseType(),
                null,
                new ReimbursementDetailInfoType(),
                null,
                new GetContactInvoiceDefaultInfoResponseType(), new GetInvoiceTitlesResponse(), null)

        then: "The result should match the expected outcome"
        result == null
    }

    def "buildDiscountAmountInfo"() {
        expect:
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildDiscountAmountInfo(null, null, null) == null
        def temp = new MapperOfBookingInitResponse.PriceInfoBuilder().buildDiscountAmountInfo(new RoomDailyInfo(customAmount: custom, salePromotionInfo: new RommDailySalePromotionEntity(promotCustomAmount: discount)), null, new RoomCouponInfoType(multiCouponAverageCustomAmount: coupon))
        res == temp?.customAmountInfo?.amount
        where:
        custom | discount | coupon || res
        null   | null     | null   || null
        2      | null     | null   || null
        2      | 1        | null   || "1"
        2      | 3        | null   || "0"
        2      | 1        | 1      || "0"
        2      | null     | 3      || "0"
    }

    @Unroll
    def "getHourRoomInfos null"() {
        given:
        def bookingRoomInfoBuilder = new MapperOfBookingInitResponse.BookingRoomInfoBuilder()

        when:
        def hourRoomDetail = new HourRoomDetail(earliestArriveTime: 0, latestLeaveTime: 1439, duration: 4, hourlyRoomTips: "钟点房 入住时间：00:00~23:59，连住4小时")
        def cancelPolicyInfo = new CancelPolicyType()
        def defaultToken = "mock no default"
        def latestLeaveTime = bookingRoomInfoBuilder.toLocalDateTime("2025-03-12", hourRoomDetail.getLatestLeaveTime())
        def earliestArriveTime = bookingRoomInfoBuilder.getStartTime(bookingRoomInfoBuilder.toLocalDateTime(
                "2025-03-12", hourRoomDetail.getEarliestArriveTime()), LocalDateTime.of(2025, 03, 12, 22, 33, 24));
        Integer cityOffsetInSecond = 480

        def res = bookingRoomInfoBuilder.getHourRoomInfos(hourRoomDetail, cancelPolicyInfo, defaultToken, latestLeaveTime, earliestArriveTime, cityOffsetInSecond)

        then:
        res.size() == 0
    }

    @Unroll
    def "getHourRoomInfos not null"() {
        given:
        def bookingRoomInfoBuilder = new MapperOfBookingInitResponse.BookingRoomInfoBuilder()

        when:
        def hourRoomDetail = new HourRoomDetail(earliestArriveTime: 0, latestLeaveTime: 1439, duration: 4, hourlyRoomTips: "钟点房 入住时间：00:00~23:59，连住4小时")
        def cancelPolicyInfo = new CancelPolicyType()
        def defaultToken = "mock no default"
        def latestLeaveTime = bookingRoomInfoBuilder.toLocalDateTime("2025-03-12", hourRoomDetail.getLatestLeaveTime())
        def earliestArriveTime = bookingRoomInfoBuilder.getStartTime(bookingRoomInfoBuilder.toLocalDateTime(
                "2025-03-12", hourRoomDetail.getEarliestArriveTime()), LocalDateTime.of(2025, 03, 12, 20, 33, 24));
        Integer cityOffsetInSecond = 480

        def res = bookingRoomInfoBuilder.getHourRoomInfos(hourRoomDetail, cancelPolicyInfo, defaultToken, latestLeaveTime, earliestArriveTime, cityOffsetInSecond)

        then:
        res.size() > 0
    }

    @Unroll
    def "testGetCheckOverStandardRcInfoTypeByServiceChargeType with #description"() {
        given: "A MapperOfBookingInitResponse instance"
        def checkTravelPolicyResponseType = new CheckTravelPolicyResponseType(
                checkRcResult: new CheckRcResultType(
                        overStandardRc: Arrays.asList(
                                new CheckOverStandardRcInfoType(serviceChargeType: "CORP_PAY_SERVICE_CHARGE"),
                                new CheckOverStandardRcInfoType(serviceChargeType: "PERSONAL_PAY_SERVICE_CHARGE"),
                                new CheckOverStandardRcInfoType(serviceChargeType: "NONE")
                        )
                )
        )

        when: "Calling getCheckOverStandardRcInfoTypeByServiceChargeType method"
        def result = new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().getCheckOverStandardRcInfoTypeByServiceChargeType(checkTravelPolicyResponseType, serviceChargeType)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                    | serviceChargeType         | expectedResult
        "null response"                | HotelPayTypeEnum.CORP_PAY | new CheckOverStandardRcInfoType(serviceChargeType: "CORP_PAY_SERVICE_CHARGE")
        "valid response with CORP_PAY" | HotelPayTypeEnum.SELF_PAY | new CheckOverStandardRcInfoType(serviceChargeType: "PERSONAL_PAY_SERVICE_CHARGE")
        "valid response with SELF_PAY" | HotelPayTypeEnum.CASH     | new CheckOverStandardRcInfoType(serviceChargeType: "NONE")
    }

    def "buildMoreOrderNonEmployeeTip"() {
        given:
        def mapper = new MapperOfBookingInitResponse.ConfirmInfoBuilder()
        expect:
        mapper.buildMoreOrderNonEmployeeTip(null, null, null, null) == null
        mapper.buildMoreOrderNonEmployeeTip(null, new BookInitToken(), null, null) == null
        mapper.buildMoreOrderNonEmployeeTip([new StrategyInfo(strategyKey: "BOOKING_SCENARIO")], null, null, null) == null
        mapper.buildMoreOrderNonEmployeeTip([new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MORE_ORDER")], null, null, null) == null
        mapper.buildMoreOrderNonEmployeeTip([new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MORE_ORDER")], null, new QueryHotelOrderDataResponseType(), null) == null
        mapper.buildMoreOrderNonEmployeeTip([new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MORE_ORDER")], null, new QueryHotelOrderDataResponseType(stakeholderList: []), null) == null
        mapper.buildMoreOrderNonEmployeeTip([new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MORE_ORDER")], null, new QueryHotelOrderDataResponseType(stakeholderList: [new StakeholderType()]), null) == null
        mapper.buildMoreOrderNonEmployeeTip([new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MORE_ORDER")], null, new QueryHotelOrderDataResponseType(stakeholderList: [new StakeholderType(userType: "Resident")]), null) == null
        mapper.buildMoreOrderNonEmployeeTip([new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MORE_ORDER")], null, new QueryHotelOrderDataResponseType(stakeholderList: [new StakeholderType(userType: "Resident", infoId: "0")]), null) == null
        mapper.buildMoreOrderNonEmployeeTip([new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MORE_ORDER")], null, new QueryHotelOrderDataResponseType(stakeholderList: [new StakeholderType(userType: "Resident", infoId: "1")]), null).code == "MORE_ORDER_NON_EMPLOYEE_TIP"
        mapper.buildMoreOrderNonEmployeeTip([new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MORE_ORDER")], null, new QueryHotelOrderDataResponseType(stakeholderList: [new StakeholderType(userType: "Resident", infoId: "1", userName: "a")]), null).confirmDetailExtends*.value == ["a"]
        mapper.buildMoreOrderNonEmployeeTip([new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MORE_ORDER")], null, new QueryHotelOrderDataResponseType(stakeholderList: [new StakeholderType(userType: "Resident", infoId: "1", firstName: "a", lastName: "b")]), null).confirmDetailExtends*.value == ["b/a"]
        mapper.buildMoreOrderNonEmployeeTip([new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MORE_ORDER")], null, new QueryHotelOrderDataResponseType(stakeholderList: [new StakeholderType(userType: "Resident", infoId: "1", firstName: "a", lastName: "b")]), new BookingInitRequestType(hotelBookInput: new HotelBookInput(adultQuantity: 2))) == null
        mapper.buildMoreOrderNonEmployeeTip([new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MORE_ORDER")], null, new QueryHotelOrderDataResponseType(hotelInfo: new HotelInfoType(hotelProduct: new HotelProductType(roomQuantity: 1)), stakeholderList: [new StakeholderType(userType: "Resident", infoId: "1", firstName: "a", lastName: "b")]), new BookingInitRequestType(hotelBookInput: new HotelBookInput(adultQuantity: 1, roomQuantity: 2))) == null

    }

    def "buildConfirmInfo"() {
        given:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                return true
            }
        }
        def check = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        check.getBookingRules() >> new BookingRulesType(cancelPolicyInfo: new CancelPolicyType(cancelType: "BIT"))
        check.isNeedGuarantee() >> true
        def mapper = new MapperOfBookingInitResponse.ConfirmInfoBuilder()
        expect:
        mapper.buildConfirmInfo(new BookingInitAssembleRequest(checkAvailInfo:  check,bookingInitRequest: new BookingInitRequestType(), resourceToken: new ResourceToken(roomResourceToken: new RoomResourceToken(balanceType: "FG")))).confirmDetailInfos*.code == ["NO_SUPPORT_GUARANTEE_PAY"]
    }


    def "getRoomAttribute"() {
        given:
        MapperOfBookingInitResponse.BookingRoomInfoBuilder builder = new MapperOfBookingInitResponse.BookingRoomInfoBuilder()
        new MockUp<MealUtil>() {
            @Mock
            public static String getRoomMealTip(Integer mealType, List<DailyMealInfo> dailyMealInfoList) {
                return "mealTipe"
            }

            @Mock
            public static String getMealRemark(Integer mealType, List<DailyMealInfo> dailyMealInfoList) {
                return "mealRemark"
            }
        }
        def input = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        expect:
        "mealTipe" == builder.getRoomAttributeValByType(RoomAttributeEnum.MEAL_DESC, input, null, null, null)
        "mealRemark" == builder.getRoomAttributeValByType(RoomAttributeEnum.MEAL_REMARK, input, null, null, null)

    }

    @Unroll
    def "testBuildHotelInvoiceDetailInfo with #description"() {
        given: "A mocked BookingInitAssembleRequest"
        def request = Mock(BookingInitAssembleRequest)
        request.getCorpOrderInvoiceDetailInfoQueryResponse() >> new CorpOrderInvoiceDetailInfoQueryResponse(hotelInvoiceDetailInfoList: [new HotelInvoiceDetailInfo(hotelFeeInfo: new HotelFeeInfo(feeType: "HotelFee", transactionFlag: "PRBAL"))])
        request.getRoomPayType() >> HotelPayTypeEnum.PRBAL

        when: "Calling buildHotelInvoiceDetailInfo"
        def result = new MapperOfBookingInitResponse.InvoiceTipInfoBuilder().buildHotelInvoiceDetailInfo(request)

        then: "The result should match the expected behavior"
        result.hotelFeeInfo.transactionFlag == "PRBAL"
    }

    def "buildTripInfoOutput"() {
        expect:
        new MapperOfBookingInitResponse.TripInfoOutputBuilder().buildTripInfoOutput(req)?.tripId == res
        where:
        req                                                                                                                        || res
        BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType())
                .build()                                                                                                               || null
        BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType())
                .withSearchTripDetailResponseType(new SearchTripDetailResponseType())
                .build()                                                                                                               || null
        BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType())
                .withSearchTripDetailResponseType(new SearchTripDetailResponseType(basicInfo: new BasicInfo()))
                .build()                                                                                                               || null
        BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType())
                .withSearchTripDetailResponseType(new SearchTripDetailResponseType(basicInfo: new BasicInfo(tripId: 0)))
                .build()                                                                                                               || null
        BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType())
                .withSearchTripDetailResponseType(new SearchTripDetailResponseType(basicInfo: new BasicInfo(tripId: 123L, tripName: "测试行程")))
                .build()                                                                                                               || "123"
    }

    def "TripInfoOutputBuilder.buildTripInfoOutput - 审批沿用场景 - 行程ID为零"() {
        given:
        def builder = new MapperOfBookingInitResponse.TripInfoOutputBuilder()
        def request = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType())
                .withStrategyInfoMap([:])
                .withSearchTripBasicInfoResponseTypeOfOriginalOrder(new SearchTripBasicInfoResponseType(basicInfo: new BasicInfo(tripId: 0L, tripName: "测试行程")))
                .build()

        and: "Mock静态方法"
        GroovyMock(BookingInitUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        BookingInitUtil.approvalFlowReuseNew(_, _) >> true
        TemplateNumberUtil.isNotZeroAndNull(_) >> false

        when:
        def result = builder.buildTripInfoOutput(request)

        then:
        result == null
    }


    def "TripInfoOutputBuilder.buildTripInfoOutput - 审批沿用场景 - basicInfo为null"() {
        given:
        def builder = new MapperOfBookingInitResponse.TripInfoOutputBuilder()
        def request = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType())
                .withStrategyInfoMap([:])
                .withSearchTripBasicInfoResponseTypeOfOriginalOrder(new SearchTripBasicInfoResponseType(basicInfo: null))
                .build()

        and: "Mock静态方法"
        GroovyMock(BookingInitUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        BookingInitUtil.approvalFlowReuseNew(_, _) >> true
        TemplateNumberUtil.isNotZeroAndNull(_) >> false

        when:
        def result = builder.buildTripInfoOutput(request)

        then:
        result == null
    }

    def "TripInfoOutputBuilder.buildTripInfoOutput - 审批沿用场景 - searchTripBasicInfoResponseType为null"() {
        given:
        def builder = new MapperOfBookingInitResponse.TripInfoOutputBuilder()
        def request = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType())
                .withStrategyInfoMap([:])
                .withSearchTripBasicInfoResponseTypeOfOriginalOrder(null)
                .build()

        and: "Mock静态方法"
        GroovyMock(BookingInitUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        BookingInitUtil.approvalFlowReuseNew(_, _) >> true
        TemplateNumberUtil.isNotZeroAndNull(_) >> false

        when:
        def result = builder.buildTripInfoOutput(request)

        then:
        result == null
    }

    def "TripInfoOutputBuilder.buildTripInfoOutput - 审批沿用为false"() {
        given:
        def builder = new MapperOfBookingInitResponse.TripInfoOutputBuilder()
        def request = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType())
                .withStrategyInfoMap([:])
                .withSearchTripBasicInfoResponseTypeOfOriginalOrder(new SearchTripBasicInfoResponseType(basicInfo: new BasicInfo(tripId: 123L, tripName: "测试行程")))
                .build()

        and: "Mock静态方法"
        GroovyMock(BookingInitUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        BookingInitUtil.approvalFlowReuseNew(_, _) >> false
        TemplateNumberUtil.isNotZeroAndNull(_) >> true

        when:
        def result = builder.buildTripInfoOutput(request)

        then:
        result == null
    }

    def "TripInfoOutputBuilder.buildTripInfoOutput - 非审批沿用场景 - searchTripDetailResponseType为null"() {
        given:
        def builder = new MapperOfBookingInitResponse.TripInfoOutputBuilder()
        def request = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType())
                .withStrategyInfoMap([:])
                .withSearchTripDetailResponseType(null)
                .build()

        and: "Mock静态方法"
        GroovyMock(BookingInitUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        BookingInitUtil.approvalFlowReuseNew(_, _) >> false
        TemplateNumberUtil.isZeroOrNull(_) >> true

        when:
        def result = builder.buildTripInfoOutput(request)

        then:
        result == null
    }

    def "TripInfoOutputBuilder.buildTripInfoOutput - 非审批沿用场景 - basicInfo为null"() {
        given:
        def builder = new MapperOfBookingInitResponse.TripInfoOutputBuilder()
        def request = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType())
                .withStrategyInfoMap([:])
                .withSearchTripDetailResponseType(new SearchTripDetailResponseType(basicInfo: null))
                .build()

        and: "Mock静态方法"
        GroovyMock(BookingInitUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        BookingInitUtil.approvalFlowReuseNew(_, _) >> false
        TemplateNumberUtil.isZeroOrNull(_) >> true

        when:
        def result = builder.buildTripInfoOutput(request)

        then:
        result == null
    }


    def "TripInfoOutputBuilder.buildTripInfoOutput - 非审批沿用场景 - tripId为零"() {
        given:
        def builder = new MapperOfBookingInitResponse.TripInfoOutputBuilder()
        def request = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType())
                .withStrategyInfoMap([:])
                .withSearchTripDetailResponseType(new SearchTripDetailResponseType(basicInfo: new BasicInfo(tripId: 0L, tripName: "测试行程")))
                .build()

        and: "Mock静态方法"
        GroovyMock(BookingInitUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        BookingInitUtil.approvalFlowReuseNew(_, _) >> false
        TemplateNumberUtil.isZeroOrNull(_) >> true

        when:
        def result = builder.buildTripInfoOutput(request)

        then:
        result == null
    }

    def "TripInfoOutputBuilder.buildTripInfoOutput - 非审批沿用场景 - tripId非零，正常返回"() {
        given:
        def builder = new MapperOfBookingInitResponse.TripInfoOutputBuilder()
        def request = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType())
                .withStrategyInfoMap([:])
                .withSearchTripDetailResponseType(new SearchTripDetailResponseType(basicInfo: new BasicInfo(tripId: 456L, tripName: "正常行程")))
                .build()

        and: "Mock静态方法"
        GroovyMock(BookingInitUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        BookingInitUtil.approvalFlowReuseNew(_, _) >> false
        TemplateNumberUtil.isZeroOrNull(_) >> false

        when:
        def result = builder.buildTripInfoOutput(request)

        then:
        result != null
        result.tripId == "456"
        result.tripName == "正常行程"
        result.tripIdSource == null
    }


    def "TripInfoOutputBuilder.buildTripInfoOutput - 完整流程测试 - 审批沿用失败，返回详情行程信息"() {
        given:
        def builder = new MapperOfBookingInitResponse.TripInfoOutputBuilder()
        def request = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType())
                .withStrategyInfoMap([:])
                .withSearchTripBasicInfoResponseTypeOfOriginalOrder(new SearchTripBasicInfoResponseType(basicInfo: new BasicInfo(tripId: 0L, tripName: "原单行程")))
                .withSearchTripDetailResponseType(new SearchTripDetailResponseType(basicInfo: new BasicInfo(tripId: 456L, tripName: "详情行程")))
                .build()

        and: "Mock静态方法"
        GroovyMock(BookingInitUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        BookingInitUtil.approvalFlowReuseNew(_, _) >> true
        TemplateNumberUtil.isNotZeroAndNull(_) >> { Long val ->
            if (val == 0L) return false
            if (val == 456L) return true
            return false
        }
        TemplateNumberUtil.isZeroOrNull(_) >> { Long val ->
            if (val == 456L) return false
            return true
        }

        when:
        def result = builder.buildTripInfoOutput(request)

        then:
        result != null
        result.tripId == "456"
        result.tripName == "详情行程"
        result.tripIdSource == null
    }

    def "TripInfoOutputBuilder.buildTripInfoOutput - 完整流程测试 - 审批沿用为false，返回详情行程信息"() {
        given:
        def builder = new MapperOfBookingInitResponse.TripInfoOutputBuilder()
        def request = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType())
                .withStrategyInfoMap([:])
                .withSearchTripBasicInfoResponseTypeOfOriginalOrder(new SearchTripBasicInfoResponseType(basicInfo: new BasicInfo(tripId: 123L, tripName: "原单行程")))
                .withSearchTripDetailResponseType(new SearchTripDetailResponseType(basicInfo: new BasicInfo(tripId: 456L, tripName: "详情行程")))
                .build()

        and: "Mock静态方法"
        GroovyMock(BookingInitUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        BookingInitUtil.approvalFlowReuseNew(_, _) >> false
        TemplateNumberUtil.isNotZeroAndNull(_) >> { Long val ->
            if (val == 123L) return true
            if (val == 456L) return true
            return false
        }
        TemplateNumberUtil.isZeroOrNull(_) >> { Long val ->
            if (val == 456L) return false
            return true
        }

        when:
        def result = builder.buildTripInfoOutput(request)

        then:
        result != null
        result.tripId == "456"
        result.tripName == "详情行程"
        result.tripIdSource == null
    }

    def "TripInfoOutputBuilder.buildTripInfoOutput - 完整流程测试 - 所有场景都失败，返回null"() {
        given:
        def builder = new MapperOfBookingInitResponse.TripInfoOutputBuilder()
        def request = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType())
                .withStrategyInfoMap([:])
                .withSearchTripBasicInfoResponseTypeOfOriginalOrder(new SearchTripBasicInfoResponseType(basicInfo: new BasicInfo(tripId: 0L, tripName: "原单行程")))
                .withSearchTripDetailResponseType(new SearchTripDetailResponseType(basicInfo: new BasicInfo(tripId: 0L, tripName: "详情行程")))
                .build()

        and: "Mock静态方法"
        GroovyMock(BookingInitUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        BookingInitUtil.approvalFlowReuseNew(_, _) >> true
        TemplateNumberUtil.isNotZeroAndNull(_) >> { Long val ->
            if (val == 0L) return false
            return false
        }
        TemplateNumberUtil.isZeroOrNull(_) >> { Long val ->
            if (val == 0L) return true
            return true
        }

        when:
        def result = builder.buildTripInfoOutput(request)

        then:
        result == null
    }

    def "TripInfoOutputBuilder.RE_BOOK_REUSE常量测试"() {
        expect:
        MapperOfBookingInitResponse.TripInfoOutputBuilder.RE_BOOK_REUSE == "RE_BOOK_REUSE"
    }

    def "buildOrderBaseInfo"() {
        expect:
        new MapperOfBookingInitResponse.OrderBaseInfoBuilder().buildOrderBaseInfo(req)?.orderInsuranceInfo?.status == res
        where:
        req                                                                                                                                                                                                            || res
        new BookingInitAssembleRequest()                                                                                                                                                                               || null
        new BookingInitAssembleRequest(xProductEnquireResponseType: new XProductEnquireResponseType())                                                                                                                 || null
        new BookingInitAssembleRequest(xProductEnquireResponseType: new XProductEnquireResponseType(responseCode: 20001))                                                                                              || null
        new BookingInitAssembleRequest(xProductEnquireResponseType: new XProductEnquireResponseType(responseCode: 20000, xProductOrderList: [null]))                                                                   || "other"
        new BookingInitAssembleRequest(xProductEnquireResponseType: new XProductEnquireResponseType(responseCode: 20000, xProductOrderList: [null], orderInsuranceExtend: new XOrderInsuranceEnquireType(scene: "A"))) || "A"
        new BookingInitAssembleRequest(xProductEnquireResponseType: new XProductEnquireResponseType(responseCode: 20000, xProductOrderList: []))                                                                       || null
    }

    def "getGroupInfoToken"() {
        given:
        new MockUp<TokenParseUtil>() {
            @Mock
            public static <T> String generateToken(T pidEntity, Class<T> tClass) {
                Assert.assertEquals("A", ((GroupInfoToken) pidEntity).getGroupRegisterRule())
                return ""
            }
        }
        expect:
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().getGroupInfoToken(new HotelBrandItem(), new GroupMemberShipType(groupRegisterRule: "A")) == ""

    }

    def "testBuildInitConfigInfos"() {
        given:
        new MockUp<MapperOfBookingInitResponse.BookingConfigInfoBuilder>() {
            @Mock
            private InitConfigInfo buildNeedApprovalNoOrEmergency(BookingInitAssembleRequest bookInitAssembleRequest,
                                                                  HotelPayTypeEnum defaultPayType) {
                return null
            }

            @Mock
            private InitConfigInfo buildSupportRebookTripApprovalFlow(BookingInitAssembleRequest bookInitAssembleRequest) {
                return null
            }

            @Mock
            private InitConfigInfo buildSupportApplication(BookingInitAssembleRequest bookInitAssembleRequest) {
                return null
            }

            @Mock
            private InitConfigInfo buildOnlySupportApplication(BookingInitAssembleRequest bookInitAssembleRequest) {
                return null
            }

            @Mock
            private InitConfigInfo buildSupportContactMemory(BookingInitAssembleRequest bookInitAssembleRequest) {
                return null
            }

            @Mock
            private InitConfigInfo buildSupportCorpPayment(BookingInitAssembleRequest bookInitAssembleRequest) {
                return null
            }

            @Mock
            private InitConfigInfo buildSupportPersonalPayment(BookingInitAssembleRequest bookInitAssembleRequest) {
                return null
            }
        }
        CheckAvailResponseType checkAvailResponseType = new CheckAvailResponseType()

        HotelRatePlan hotelRatePlan = new HotelRatePlan()
        checkAvailResponseType.setHotelRatePlan(hotelRatePlan)
        hotelRatePlan.setHotelInfo(new HotelItem(city: 1))
        hotelRatePlan.setRoomInfo(new RoomItem(salePromotionInfo: new SalePromotionEntity(), balanceType: "FG"))

        ResourceToken resourceToken = new ResourceToken()
        resourceToken.setRoomResourceToken(new RoomResourceToken())
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(resourceToken).build().getCheckAvailInfo();
        def bookingInitAssembleRequest = Mock(BookingInitAssembleRequest) {
            getBookingInitRequest() >> new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "private"), integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001")))
            getCheckAvailInfo() >> checkAvailInfo
            getRoomPayType() >> HotelPayTypeEnum.CORP_PAY
            getAccountInfo() >> Mock(WrapperOfAccount.AccountInfo)
        }
        when:
        def result = new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildInitConfigInfos(bookingInitAssembleRequest, null, null)
        then:
        result.size() != 0
    }

    def "buildPriceDeductionMemberPointInfo"() {
        given:
        def checkAvail = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        checkAvail.getBookingRules() >> new BookingRulesType(memberPointsPolicyInfo: new MemberPointsPolicyInfoType(bonusInfo: bonus))
        expect:
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().buildPriceDeductionMemberPointInfo(null) == null
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().buildPriceDeductionMemberPointInfo(checkAvail)?.totalPoint == res
        where:
        bonus                                                                                                    || res
        null                                                                                                     || null
        new BonusInfoType()                                                                                      || null
        new BonusInfoType(bonusDetailList: [])                                                                   || null
        new BonusInfoType(bonusDetailList: [new BonusDetailInfoType()])                                          || null
        new BonusInfoType(bonusDetailList: [new BonusDetailInfoType(bonusType: 2)])                              || null
        new BonusInfoType(bonusDetailList: [new BonusDetailInfoType(bonusType: 1, sceneType: 1)])                || null
        new BonusInfoType(bonusDetailList: [new BonusDetailInfoType(bonusType: 1, sceneType: 2)])                || null
        new BonusInfoType(bonusDetailList: [new BonusDetailInfoType(bonusType: 1, sceneType: 2, totalBonus: 2)]) || "2"

    }

    def "buildPointDeductionAmountInfo"() {
        expect:
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().buildPointDeductionAmountInfo(null) == null
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().buildPointDeductionAmountInfo(new CustomBonusValueInfoType()) == null
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().buildPointDeductionAmountInfo(new CustomBonusValueInfoType(currency: "CNY")) == null
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().buildPointDeductionAmountInfo(new CustomBonusValueInfoType(currency: "CNY", totalAmount: 2)).amount == "2"
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().buildPointDeductionAmountInfo(new CustomBonusValueInfoType(currency: "CNY", totalAmount: 2.0)).amount == "2"
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().buildPointDeductionAmountInfo(new CustomBonusValueInfoType(currency: "CNY", totalAmount: 1.20)).amount == "1.2"
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().buildPointDeductionAmountInfo(new CustomBonusValueInfoType(currency: "CNY", totalAmount: 10.0)).amount == "10"
    }

    def "buildHotelBrandMultiLanguageGroupName"() {
        expect:
        new MapperOfBookingInitResponse.BookingHotelInfoBuilder().buildHotelBrandMultiLanguageGroupName(null).attributeValue == null
        new MapperOfBookingInitResponse.BookingHotelInfoBuilder().buildHotelBrandMultiLanguageGroupName(new HotelItem()).attributeValue == null
        new MapperOfBookingInitResponse.BookingHotelInfoBuilder().buildHotelBrandMultiLanguageGroupName(new HotelItem(hotelBrandInfo: new HotelBrandItem())).attributeValue == null
        new MapperOfBookingInitResponse.BookingHotelInfoBuilder().buildHotelBrandMultiLanguageGroupName(new HotelItem(hotelBrandInfo: new HotelBrandItem(multiLanguageGroupName: "a"))).attributeValue == "a"
    }

    def "buildLocaleCityName"() {
        expect:
        new MapperOfBookingInitResponse.BookingHotelInfoBuilder().buildLocaleCityName(null, null) == null
        new MapperOfBookingInitResponse.BookingHotelInfoBuilder().buildLocaleCityName(1, null) == null
        new MapperOfBookingInitResponse.BookingHotelInfoBuilder().buildLocaleCityName(1, new GetCityBaseInfoResponseType()) == null
        new MapperOfBookingInitResponse.BookingHotelInfoBuilder().buildLocaleCityName(1, new GetCityBaseInfoResponseType(cityBaseInfo: [])) == null
        new MapperOfBookingInitResponse.BookingHotelInfoBuilder().buildLocaleCityName(1, new GetCityBaseInfoResponseType(cityBaseInfo: [new CityBaseInfoEntity()])) == null
        new MapperOfBookingInitResponse.BookingHotelInfoBuilder().buildLocaleCityName(1, new GetCityBaseInfoResponseType(cityBaseInfo: [new CityBaseInfoEntity(cityId: 2)])) == null
        new MapperOfBookingInitResponse.BookingHotelInfoBuilder().buildLocaleCityName(1, new GetCityBaseInfoResponseType(cityBaseInfo: [new CityBaseInfoEntity(cityId: 1, cityName: "a")])) == "a"
    }

    def "buildProvinceInfo"() {
        expect:
        new MapperOfBookingInitResponse.BookingHotelInfoBuilder().buildProvinceInfo(1, 2).provinceId == 1
        new MapperOfBookingInitResponse.BookingHotelInfoBuilder().buildProvinceInfo(1, 2).countryInfo.countryId == 2
    }

    def "buildNationalityRestrictionInfos"() {
        expect:
        new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildNationalityRestrictionInfos(null, null) == null
        new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildNationalityRestrictionInfos(new NationalityRestrictionType(), null) == []
        new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildNationalityRestrictionInfos(new NationalityRestrictionType(allowCountryCodeList: ["CN"]), null)*.code == ["CN"]
        new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildNationalityRestrictionInfos(new NationalityRestrictionType(blockCountryCodeList: ["CN"]), null)*.code == ["CN"]
    }

    def "buildPhoneCountryCodeLimitInfos"() {
        expect:
        new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildPhoneCountryCodeLimitInfos(null, null, null, null, null) == null
        new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildPhoneCountryCodeLimitInfos(["CN"], null, null, null, null)*.countryCode == ["CN"]
    }

    def "buildCategory"() {
        expect:
        new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildCategory(null, null) == null
        new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildCategory(new Nationality(pyhead: "a"), "zh-CN") == "a"
        new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildCategory(new Nationality(pyhead: "a"), "zh-HK") == "a"
        new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildCategory(new Nationality(headletter: "b"), "en-US") == "b"
    }

    def "buildCountryInfo"() {
        expect:
        new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildCountryInfo(null) == null
        new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildCountryInfo(new Nationality(cname: "a")).localeCountryName == "a"
        new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildCountryInfo(new Nationality(ename: "b")).localeCountryName == "b"
    }

    def "testBuildBookingInvoiceInfoOfInvoice"() {
        given: "A BookingInvoiceInfoBuilder instance and mocked request"
        def builder = new MapperOfBookingInitResponse.BookingInvoiceInfoBuilder()
        def request = Mock(BookingInitAssembleRequest) {
            getCorpOrderInvoiceDetailInfoQueryResponse() >> Mock(CorpOrderInvoiceDetailInfoQueryResponse)
            getReimbursementDetailInfoType() >> new ReimbursementDetailInfoType(reimbursedInfo: new ReimbursedInfoType(eInvoiceEmail: "email"))
        }

        when: "Calling buildBookingInvoiceInfoOfInvoice with valid inputs"
        BookingInvoiceInfo result = builder.buildBookingInvoiceInfoOfInvoice(request, "SERVICE_FEE_INVOICE")

        then: "The result should not be null and should match expected values"
        result != null
        result.hotelInvoiceProductInfos.get(0).productType == "SERVICE_FEE_INVOICE"
        result.hotelInvoiceProductInfos.get(0).hotelInvoiceTypeInfos.get(0).email == "email"
    }

    def "buildInvoiceTypeNew"() {
        given:
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                if (key.equalsIgnoreCase("newInvoice")) {
                    return true
                }
                return false
            }
        }
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001")),
                hotelInsuranceInput: new HotelInsuranceInput(
                        hotelInsuranceDetailInputs: Arrays.asList(new HotelInsuranceDetailInput())))
        CorpOrderInvoiceDetailInfoQueryResponse getCorpOrderInvoiceDetailInfoQueryResponse = new CorpOrderInvoiceDetailInfoQueryResponse(
                hotelInvoiceDetailInfoList: Arrays.asList(new HotelInvoiceDetailInfo(
                        invoiceType: "INVOICE",
                        hotelFeeInfo: new HotelFeeInfo(feeType: InvoiceConstant.HOTEL_FEE, transactionFlag: "PERSONAL"),
                        travelSendType: "CorpOrder")))
        BookingInitAssembleRequest bookingInitAssembleRequest = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(bookingInitRequestType)
                .withCorpOrderInvoiceDetailInfoQueryResponse(getCorpOrderInvoiceDetailInfoQueryResponse)
                .withRoomPayType(HotelPayTypeEnum.SELF_PAY)
                .withGetContactInvoiceDefaultInfoResponseType(new GetContactInvoiceDefaultInfoResponseType(invoice: new InvoiceInfoType(invoiceEmail: "<EMAIL>")))
                .build()
        when:
        def result = new MapperOfBookingInitResponse.BookingInvoiceInfoBuilder().buildInvoiceTypeNew(bookingInitAssembleRequest)

        then:
        result != null
        result.hotelInvoiceProductInfos.size() == 1
        result.hotelInvoiceProductInfos.get(0).productType == "ROOM_FEE_INVOICE"
        result.hotelInvoiceProductInfos.get(0).hotelInvoiceTypeInfos.size() == 1
        result.hotelInvoiceProductInfos.get(0).hotelInvoiceTypeInfos.get(0).invoiceType == "INVOICE"
        result.hotelInvoiceProductInfos.get(0).hotelInvoiceTypeInfos.get(0).email == "<EMAIL>"


        when:
        getCorpOrderInvoiceDetailInfoQueryResponse = new CorpOrderInvoiceDetailInfoQueryResponse(
                hotelInvoiceDetailInfoList: Arrays.asList(new HotelInvoiceDetailInfo(
                        invoiceType: "INVOICE",
                        hotelFeeInfo: new HotelFeeInfo(feeType: InvoiceConstant.SERVICE_FEE, transactionFlag: "PERSONAL"),
                        travelSendType: "CorpOrder")))
        bookingInitAssembleRequest = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(bookingInitRequestType)
                .withCorpOrderInvoiceDetailInfoQueryResponse(getCorpOrderInvoiceDetailInfoQueryResponse)
                .withRoomPayType(HotelPayTypeEnum.CASH)
                .withServicePayType(HotelPayTypeEnum.SELF_PAY)
                .build()
        result = new MapperOfBookingInitResponse.BookingInvoiceInfoBuilder().buildInvoiceTypeNew(bookingInitAssembleRequest)

        then:
        result != null
        result.hotelInvoiceProductInfos.size() == 1
        result.hotelInvoiceProductInfos.get(0).productType == "SERVICE_FEE_INVOICE"
        result.hotelInvoiceProductInfos.get(0).hotelInvoiceTypeInfos.size() == 1
        result.hotelInvoiceProductInfos.get(0).hotelInvoiceTypeInfos.get(0).invoiceType == "INVOICE"
    }

    @Unroll
    def "modifyPersonNumber"() {
        given:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                if ("ali".equalsIgnoreCase(corpId)) {
                    return true;
                }
                return false;
            }
        }
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                corpPayInfo: new CorpPayInfo(corpPayType: corpPayType),
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: corpIdinput)),
                hotelInsuranceInput: new HotelInsuranceInput(
                        hotelInsuranceDetailInputs: Arrays.asList(new HotelInsuranceDetailInput())))
        Map<String, StrategyInfo> strategyInfoMap = new HashMap<>();
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isPolicyModel() >> policyModel
            isTravelStandPolicy() >> travelStandPolicy
            isSingleBooking() >> singleBooking
        }
        strategyInfoMap.put("BOOKING_SCENARIO", new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: bookingScenarioValue))
        BookingInitAssembleRequest bookInitAssembleRequest = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(bookingInitRequestType).withAccountInfo(accountInfo)
                .withStrategyInfoMap(strategyInfoMap)
                .withResourceToken(new ResourceToken(hotelResourceToken: new HotelResourceToken(hotelGeoInfoResourceToken: new HotelGeoInfoResourceToken())))
                .build()
        expect:
        result == new MapperOfBookingInitResponse.BookingConfigInfoBuilder().modifyPersonNumber(bookInitAssembleRequest)
        where:
        corpIdinput | corpPayType | bookingScenarioValue | policyModel | travelStandPolicy | singleBooking || result
        "testnet"   | "private"   | "EXTEND"             | false       | false             | false         || true
        "testnet"   | "public"    | "BOOK"               | false       | false             | false         || true
        "testnet"   | "public"    | "EXTEND"             | false       | false             | false         || false
        "testnet"   | "public"    | "APPLY_MODIFY"       | false       | false             | false         || false
        "ali"       | "public"    | "BOOK"               | false       | false             | false         || false
        "testnet"   | "public"    | "BOOK"               | true        | false             | false         || true
        "testnet"   | "public"    | "BOOK"               | true        | false             | true          || false
        "testnet"   | "public"    | "BOOK"               | false       | true              | false         || false
    }


    @Unroll
    def "buildShareRoomCanChangePassenger"() {
        given:
        def tester = Spy(new MapperOfBookingInitResponse.BookingConfigInfoBuilder())
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                corpPayInfo: new CorpPayInfo(corpPayType: new CorpPayInfo(corpPayType: "private")),
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "corpid")),
                hotelInsuranceInput: new HotelInsuranceInput(
                        hotelInsuranceDetailInputs: Arrays.asList(new HotelInsuranceDetailInput())))
        Map<String, StrategyInfo> strategyInfoMap = new HashMap<>();
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isPolicyModel() >> policyModel
            isTravelStandPolicy() >> travelStandPolicy
        }
        BookingInitAssembleRequest bookInitAssembleRequest = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(bookingInitRequestType).withAccountInfo(accountInfo)
                .withStrategyInfoMap(strategyInfoMap)
                .withResourceToken(new ResourceToken(hotelResourceToken: new HotelResourceToken(hotelGeoInfoResourceToken: new HotelGeoInfoResourceToken())))
                .build()
        tester.shareRoom(_, _, _) >> shareRoom
        tester.buildSelfCardForbidModifyRoomNumber(_, _, _) >> selfCardForbidModifyRoomNumber
        expect:
        result == tester.buildShareRoomCanChangePassenger(bookInitAssembleRequest)
        where:
        shareRoom | policyModel | travelStandPolicy | selfCardForbidModifyRoomNumber || result
        true      | false       | false             | false                          || true
    }

    def "buildRemarkWorkLengthLimit"() {
        given:

        expect:
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildRemarkWorkLengthLimit("A", false) == null
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildRemarkWorkLengthLimit("B", true) == "B"


    }

    def "buildNoFeelTrip"() {
        given:
        def account = Mock(WrapperOfAccount.AccountInfo)
        account.isPreApprovalRequired(_, _) >> isPreApprovalRequired
        account.isTravelApplyRequired(_, _) >> isTravelApplyRequired
        account.isTripMode() >> isTripMode
        account.isSenItinerary() >> isSenItinerary
        def corpPay = new CorpPayInfo(corpPayType: corpPayType)
        expect:
        res == new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildNoFeelTrip(account, corpPay, null)

        where:
        isPreApprovalRequired | isTravelApplyRequired | isTripMode | isSenItinerary | corpPayType || res
        false                 | false                 | false      | false          | "private"   || false
        true                  | false                 | false      | false          | "public"    || false
        false                 | true                  | false      | false          | "public"    || false
        false                 | false                 | true       | false          | "public"    || false
        false                 | false                 | true       | true           | "public"    || true
    }

    def "buildBillControlMode"() {
        given:
        def account = Mock(WrapperOfAccount.AccountInfo)
        account.isPreApprovalRequired(_, _) >> isPreApprovalRequired
        account.isTravelApplyRequired(_, _) >> isTravelApplyRequired

        expect:
        res == new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildBillControlMode(account, new CorpPayInfo(corpPayType: "corpPayType"), null)

        where:
        isPreApprovalRequired | isTravelApplyRequired || res
        true                  | false                  | "APPROVAL"
        false                 | true                   | "BUSINESS_APPLY"
        false                 | false                  | null
    }

    def "buildShowApprovalModule"() {
        given:
        def account = Mock(WrapperOfAccount.AccountInfo)
        account.isPreApprovalRequired(_, _) >> isPreApprovalRequired
        account.isTravelApplyRequired(_, _) >> isTravelApplyRequired
        account.isPersonPayNeedAdvanceAuth(_) >> isPersonPayNeedAdvanceAuth
        expect:
        new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildShowApprovalModule(new BookingInitAssembleRequest(bookingInitRequest: new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "private")))) == false
        new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildShowApprovalModule(new BookingInitAssembleRequest(bookingInitRequest: new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public")))) == false
        new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildShowApprovalModule(new BookingInitAssembleRequest(bookingInitRequest: new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public")), accountInfo: account, roomPayType: roomPayType)) == res
        where:
        isPreApprovalRequired | isTravelApplyRequired | isPersonPayNeedAdvanceAuth | roomPayType                || res
        false                 | false                 | false                      | HotelPayTypeEnum.CORP_PAY  || false
        true                  | false                 | false                      | HotelPayTypeEnum.CORP_PAY  || true
        false                 | true                  | false                      | HotelPayTypeEnum.CORP_PAY  || true
        true                  | true                  | false                      | HotelPayTypeEnum.CORP_PAY  || true
        false                 | false                 | false                      | HotelPayTypeEnum.UNION_PAY || false
        false                 | false                 | false                      | HotelPayTypeEnum.SELF_PAY  || false
        false                 | false                 | false                      | HotelPayTypeEnum.CASH      || false
        false                 | false                 | true                       | HotelPayTypeEnum.CORP_PAY  || false
    }

    def "buildLogoUrl"() {
        expect:
        new MapperOfBookingInitResponse.BookingHotelInfoBuilder().buildLogoUrl(null) == null
        new MapperOfBookingInitResponse.BookingHotelInfoBuilder().buildLogoUrl(new HotelItem()) == null
        new MapperOfBookingInitResponse.BookingHotelInfoBuilder().buildLogoUrl(new HotelItem(logoUrl: "logo")).attributeValue == "logo"
    }


    def "buildOverStandRoomIndexes"() {
        expect:
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildOverStandRoomIndexes(null) == null
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildOverStandRoomIndexes([]) == null
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildOverStandRoomIndexes([null]) == null
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildOverStandRoomIndexes([new RoomModeInfoType()]) == null
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildOverStandRoomIndexes([new RoomModeInfoType(priceInControl: true)]) == null
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildOverStandRoomIndexes([new RoomModeInfoType(priceInControl: false, roomIndex: 1), new RoomModeInfoType()]) == ["1"]
    }

    def "buildLowPriceRcInfo"() {
        given:
        new MockUp<HotelRCUtil>() {
            @Mock
            public static List<ReasoncodeInfo> getPriceReasons(GetReasoncodesResponseType getReasoncodesResponseType,
                                                               RcTypeEnum rcTypeEnum) {
                return [new ReasoncodeInfo()]
            }
        }
        expect:
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildLowPriceRcInfo(null, null, null) == null
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildLowPriceRcInfo(new CheckOverStandardRcInfoType(required: false), null, null) == null
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildLowPriceRcInfo(new CheckOverStandardRcInfoType(required: true, selected: true), null, null) == null
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildLowPriceRcInfo(new CheckOverStandardRcInfoType(required: true, selected: false), null, null).rcType == "LOW_PRICE"
    }


    def "buildRcInfos"() {
        given:

        expect:
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildRcInfos(null) == null
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildRcInfos(new BookingInitAssembleRequest(bookingInitRequest: new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "private")))) == null
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildRcInfos(new BookingInitAssembleRequest(bookingInitRequest: new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public")))) == []
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildRcInfos(new BookingInitAssembleRequest(checkTravelPolicyResponseType: new CheckTravelPolicyResponseType(),
                bookingInitRequest: new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public")))) == []
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildRcInfos(new BookingInitAssembleRequest(checkTravelPolicyResponseType: new CheckTravelPolicyResponseType(checkRcResult: new CheckRcResultType()),
                bookingInitRequest: new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public")))) == []
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildRcInfos(new BookingInitAssembleRequest(checkTravelPolicyResponseType: new CheckTravelPolicyResponseType(checkRcResult: new CheckRcResultType(checkResultList: [])),
                bookingInitRequest: new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public")))) == []
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildRcInfos(new BookingInitAssembleRequest(checkTravelPolicyResponseType: new CheckTravelPolicyResponseType(checkRcResult: new CheckRcResultType(checkResultList: [])),
                bookingInitRequest: new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public")))) == []
    }


    def "buildSummaryPrice0"() {
        expect:

        new MapperOfBookingInitResponse.PriceInfoBuilder().buildSummaryPrice(new ResourceToken(roomResourceToken: new RoomResourceToken(balanceType: "PP")),
                new DistributePaymentAmountResponseType(corporateCardPaymentAmount: new PriceType2(currency: "CNY", price: 2)), null, null, null,
                HotelPayTypeEnum.CORPORATE_CARD_PAY, null, null).customAmountInfo.amount == "2"
    }

    def "buildModifyRcInfo"() {
        given:
        new MockUp<HotelRCUtil>() {
            @Mock
            public static List<ReasoncodeInfo> getPriceReasonsWithoutException(GetReasoncodesResponseType getReasoncodesResponseType,
                                                               RcTypeEnum rcTypeEnum) {
                return [new ReasoncodeInfo()]
            }
        }
        expect:
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildModifyRcInfo(null, null, null) == null
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildModifyRcInfo([new StrategyInfo(strategyKey: "NEED_MODIFY_RC_INFO", strategyValue: "T")], null, null) == null
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildModifyRcInfo([new StrategyInfo(strategyKey: "NEED_MODIFY_RC_INFO", strategyValue: "T")], new GetReasoncodesResponseType(), null) == null
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildModifyRcInfo([new StrategyInfo(strategyKey: "NEED_MODIFY_RC_INFO", strategyValue: "T")], new GetReasoncodesResponseType(reasonCodes: []), null) == null
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildModifyRcInfo([new StrategyInfo(strategyKey: "NEED_MODIFY_RC_INFO", strategyValue: "T")], new GetReasoncodesResponseType(reasonCodes: [new ReasoncodeInfo()]), null).rcType == "MODIFY"
    }

    def "buildPhoneCountryCodeLimitInfos00"() {
        expect:
        new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildPhoneCountryCodeLimitInfos(null, null, null, 2417, [new StrategyInfo(strategyKey: "BAGASHI_CUSTOM", strategyValue: "T")])*.countryCode == ["81"]
    }

    @Unroll
    def "buildCouponAmountInfo"() {
        given:
        def result
        when:
        result = new MapperOfBookingInitResponse.PriceInfoBuilder().buildCouponAmountInfo(null, "CNY")
        then:
        result == null

        when:
        result = new MapperOfBookingInitResponse.PriceInfoBuilder().buildCouponAmountInfo(new RoomCouponInfoType(multiCouponTotalCustomAmount: 100), "CNY")
        then:
        result.getCustomAmountInfo().getAmount() == "100"
        result.getCustomAmountInfo().getCurrency() == "CNY"

    }

    def "buildRoomAmountInfo"() {
        given:
        def tester = Spy(new MapperOfBookingInitResponse.PriceInfoBuilder())
        1 * tester.buildHotelAmountInfo(_, _, _, _, _, _) >> new HotelAmountInfo()
        1 * tester.buildRoomDailyInfos(_, _, _, _, _, _, _, _) >> []
        1 * tester.buildIncludeInTotalTaxAmountInfo(_, _, _, _, _) >> new TaxAmountInfo()
        expect:
        tester.buildRoomAmountInfo(null, null, null, null, null, null, null) == null
        tester.buildRoomAmountInfo(new RoomItem(), null, null, null, null, null, null) != null

    }

    def "buildIncludeInTotalTaxAmountInfo"() {
        given:
        def tester = Spy(new MapperOfBookingInitResponse.PriceInfoBuilder())
        2 * tester.buildHotelAmountInfo(_, _, _, _, _, _) >> null >> new HotelAmountInfo()
        2 * tester.buildTaxDetailAmountInfos(_, _, _) >> null >> [null]
        expect:
        tester.buildIncludeInTotalTaxAmountInfo(null, null, null, null, null) == null
        tester.buildIncludeInTotalTaxAmountInfo(new PriceItemInfo(), null, null, null, null) == null
        tester.buildIncludeInTotalTaxAmountInfo(new PriceItemInfo(), null, null, null, null) != null

    }


    def "buildPromotItems"() {
        expect:
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildPromotItems(null) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildPromotItems([null]) == []
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildPromotItems([new PromotionsDetailInfoType()]) == []
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildPromotItems([new PromotionsDetailInfoType(customTotalPromotionsPrice: new PriceType(currency: "CNY", price: 2))]).size() == 1
    }

    def "buildCouponItems"() {
        expect:
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildCouponItems(null, null) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildCouponItems([], null) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildCouponItems([null], null) == []
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildCouponItems([new CouponInfoType()], null) == []
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildCouponItems([new CouponInfoType(bestOrChosenCoupon: false)], null) == []
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildCouponItems([new CouponInfoType(bestOrChosenCoupon: true)], null) == []
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildCouponItems([new CouponInfoType(bestOrChosenCoupon: true, totalCustomAmount: new PriceType(currency: "CNY", price: 2))], null).size() == 1
    }

    def "buildTaxDetailAmountInfos"() {
        expect:
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildTaxDetailAmountInfos(null, null, null) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildTaxDetailAmountInfos([null], null, null) == []
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildTaxDetailAmountInfos([new TaxDetailType()], null, null) == []
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildTaxDetailAmountInfos([new TaxDetailType(includeInTotalPrice: false)], true, null) == []
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildTaxDetailAmountInfos([new TaxDetailType(includeInTotalPrice: false)], false, null) == []
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildTaxDetailAmountInfos([new TaxDetailType(includeInTotalPrice: false, customCurrency: "CNY", customAmount: 2)], false, null)*.amountInfo*.customAmountInfo*.currency == ["CNY"]
    }

    def "buildPriceInfo"() {
        given:
        def tester = Spy(new MapperOfBookingInitResponse.PriceInfoBuilder())
        1 * tester.buildPriceDetailInfo(_, _, _, _, _, _, _, _, _, _, _, _) >> new PriceDetailInfo()
        1 * tester.buildSummaryPriceInfo(_, _, _, _, _, _, _, _) >> new SummaryPriceInfo()
        expect:
        tester.buildPriceInfo(new BookingInitAssembleRequest(resourceToken: new ResourceToken(), checkAvailInfo: Mock(WrapperOfCheckAvail.CheckAvailInfo), accountInfo: Mock(WrapperOfAccount.AccountInfo))) != null
    }

    def "buildPriceDetailInfo"() {
        given:
        def tester = Spy(new MapperOfBookingInitResponse.PriceInfoBuilder())
        1 * tester.buildRoomAmountInfo(_, _, _, _, _, _, _) >> new RoomAmountInfo()
        1 * tester.buildTaxExtraFeeAmountInfo(_, _, _, _, _) >> new TaxAmountInfo()
        1 * tester.buildPromotionAmountInfo(_, _, _, _, _, _) >> new PromotionAmountInfo()
        1 * tester.buildGuaranteeAmountInfo(_, _, _, _, _, _, _, _) >> new GuaranteeAmountInfo()
        1 * tester.buildMixPayAmountInfo(_, _, _, _, _) >> new MixPayAmountInfo()
        1 * tester.buildServiceFeeAmountInfo(_, _, _, _, _, _) >> new ServiceFeeAmountInfo()
        1 * tester.buildXproductAmountInfo(_) >> new XProductAmountInfo()
        1 * tester.buildOnlinePayAmountInfo(_, _, _, _, _, _, _, _) >> new HotelAmountInfo()
        2 * tester.buildHotelAmountInfo(_, _, _) >> new HotelAmountInfo()
        def checkavail = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        checkavail.getRoomItem() >> new RoomItem()
        expect:
        tester.buildPriceDetailInfo(Mock(WrapperOfCheckAvail.CheckAvailInfo), null, Mock(BookingInitRequestType), null, null, null, null, null, null, null, false, null) == null
        tester.buildPriceDetailInfo(checkavail, null, Mock(BookingInitRequestType), null, null, null, null, null, null, null, false, null) != null
    }


    def "buildTaxExtraFeeAmountInfo"() {
        given:
        def tester = Spy(new MapperOfBookingInitResponse.PriceInfoBuilder())
        2 * tester.buildHotelAmountInfo(_, _, _, _, _, _) >> null >> new HotelAmountInfo()
        2 * tester.buildTaxDetailAmountInfos(_, _, _) >> null >> [null]
        expect:
        tester.buildIncludeInTotalTaxAmountInfo(null, null, null, null, null) == null
        tester.buildIncludeInTotalTaxAmountInfo(new PriceItemInfo(), null, null, null, null) == null
        tester.buildIncludeInTotalTaxAmountInfo(new PriceItemInfo(), null, null, null, null) != null
    }


    def "isEndTimeTomorrow"() {
        expect:
        new MapperOfBookingInitResponse.PriceInfoBuilder().isEndTimeTomorrow(end, checkin) == res
        where:
        end                   | checkin      || res
        null                  | null         || false
        "2024-01-01"          | null         || false
        null                  | "2024-01-01" || false
        "2024-01-01"          | "sdd"        || false
        "2024-01-01"          | "2024-01-01" || false
        "2024-01-02 01:00:00" | "2024-01-01" || false
    }

    def "bulidDailyMealInfo"() {
        expect:
        new MapperOfBookingInitResponse.PriceInfoBuilder().bulidDailyMealInfo(null) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().bulidDailyMealInfo([]) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().bulidDailyMealInfo([null]) == []
        new MapperOfBookingInitResponse.PriceInfoBuilder().bulidDailyMealInfo([new RoomDailyInfo(localEffectDate: "a")])*.checkIn == ["a"]
    }


    def "buildXProductSelected"() {
        expect:
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildXProductSelected(null) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildXProductSelected(new HotelInsuranceInput()) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildXProductSelected(new HotelInsuranceInput(hotelInsuranceDetailInputs: [])) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildXProductSelected(new HotelInsuranceInput(hotelInsuranceDetailInputs: [new HotelInsuranceDetailInput(), null])).corpXProductInfoList == []
    }


    def "buildPassengerId"() {
        expect:
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildPassengerId(null as PassengerToken) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildPassengerId(new PassengerToken()) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildPassengerId(new PassengerToken(corp: true, uid: "a")) == "a"
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildPassengerId(new PassengerToken(corp: false, infoId: "a")) == "a"
    }

    def "buildPassengerId0"() {
        expect:
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildPassengerId(new HotelPassengerInput(uid: "a")) == "a"
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildPassengerId(new HotelPassengerInput(infoId: "a")) == "a"

    }


    def "buildDepositDescInfo"() {
        expect:
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildDepositDescInfo(null) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildDepositDescInfo([]) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildDepositDescInfo([null]) == []
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildDepositDescInfo([new DepositDescType(hotelLocalTimeDesc: "a")])*.hotelLocalTimeDesc == ["a"]
    }


    def "buildPayInfos"() {
        given:
        def tester = Spy(new MapperOfBookingInitResponse.PayInfoBuilder())
        1 * tester.buildRoomPay(_, _, _, _, _, _,_,_) >> new PayInfo()
        1 * tester.buildServicePay(_, _, _, _) >> new PayInfo()
        1 * tester.buildGuaranteePayInfo(_, _, _, _) >> new PayInfo()
        expect:
        tester.buildPayInfos(new BookingInitAssembleRequest()).size() == 3

    }


    def "buildRoomPay"() {
        expect:
        new MapperOfBookingInitResponse.PayInfoBuilder().buildRoomPay(null, null, null, new BookingInitRequestType(), null, null, null,null).code == "ROOM"
    }

    def "buildRoomPayTypeInfo"() {
        expect:
        new MapperOfBookingInitResponse.PayInfoBuilder().buildRoomPayTypeInfo(null, null, null, null, null, null, null, null, null, null, null) == null
        new MapperOfBookingInitResponse.PayInfoBuilder().buildRoomPayTypeInfo([], null, null, null, null, null, null, null, null, null, null) == null
        new MapperOfBookingInitResponse.PayInfoBuilder().buildRoomPayTypeInfo([null], null, null, null, null, null, null, null, null, null, null) == []
        new MapperOfBookingInitResponse.PayInfoBuilder().buildRoomPayTypeInfo([new PaymentMethodInfoType()], null, null, null, null, null, null, null, null, null, null) == []
        new MapperOfBookingInitResponse.PayInfoBuilder().buildRoomPayTypeInfo([new PaymentMethodInfoType(paymentMethod: "FLASH_STAY_PAY")], false, null, null, null, null, null, null, null, null, null) == []
        new MapperOfBookingInitResponse.PayInfoBuilder().buildRoomPayTypeInfo([new PaymentMethodInfoType(paymentMethod: "FLASH_STAY_PAY")], true, true, HotelBalanceTypeEnum.FG, null, Mock(WrapperOfAccount.AccountInfo), null, Mock(WrapperOfCheckAvail.CheckAvailInfo), null, null, null)*.payType == ["FLASH_STAY_PAY"]
    }

    def "buildServicePay"() {
        expect:
        new MapperOfBookingInitResponse.PayInfoBuilder().buildServicePay(null, null, HotelPayTypeEnum.PRBAL, null) == null
        new MapperOfBookingInitResponse.PayInfoBuilder().buildServicePay(null, null, HotelPayTypeEnum.CASH, null).code == "SERVICE"
    }


    def "buildServicePayTypes"() {
        expect:
        new MapperOfBookingInitResponse.PayInfoBuilder().buildServicePayTypes(null, null) == null
        new MapperOfBookingInitResponse.PayInfoBuilder().buildServicePayTypes(null, []) == null
        new MapperOfBookingInitResponse.PayInfoBuilder().buildServicePayTypes(null, [null]) == []
        new MapperOfBookingInitResponse.PayInfoBuilder().buildServicePayTypes(null, [new ServiceChargePaymentMethodInfoType()]) == []
        new MapperOfBookingInitResponse.PayInfoBuilder().buildServicePayTypes(null, [new ServiceChargePaymentMethodInfoType(paymentMethod: "ACCOUNT_PAY")])*.payType == ["CORP_PAY"]
    }

    def "buildPrepayType"() {
        expect:
        new MapperOfBookingInitResponse.PayInfoBuilder().buildPrepayType(balance, force) == res
        where:
        balance                    | force || res
        null                       | true  || "PRE_PAY_HOTEL"
        null                       | false || null
        HotelBalanceTypeEnum.USEFG | false || "PRE_PAY_CTRIP"
        HotelBalanceTypeEnum.PP    | false || "PRE_PAY_CTRIP"
        HotelBalanceTypeEnum.PH    | false || "PRE_PAY_HOTEL"
        HotelBalanceTypeEnum.FG    | false || "NOW_PAY_HOTEL"
    }

    def "buildPayStep"() {
        expect:
        new MapperOfBookingInitResponse.PayInfoBuilder().buildPayStep(balance, roomPayType, guarantee) == res
        where:
        balance                    | roomPayType                     | guarantee                             || res
        null                       | null                            | null                                  || null
        HotelBalanceTypeEnum.FG    | null                            | HotelGuaranteeTypeEnum.CORP_GUARANTEE || "GUARANTEE"
        HotelBalanceTypeEnum.FG    | null                            | null                                  || "CASH"
        HotelBalanceTypeEnum.PH    | null                            | null                                  || "GUARANTEE"
        HotelBalanceTypeEnum.PP    | HotelPayTypeEnum.CORP_PAY       | null                                  || "CASH"
        HotelBalanceTypeEnum.PP    | HotelPayTypeEnum.FLASH_STAY_PAY | null                                  || "CASH"
        HotelBalanceTypeEnum.PP    | HotelPayTypeEnum.ADVANCE_PAY    | null                                  || "CASH"
        HotelBalanceTypeEnum.USEFG | null                            | null                                  || "PREPAY"
    }


    def "buildInsuranceOnlyPayType"() {
        expect:
        !new MapperOfBookingInitResponse.PayInfoBuilder().buildInsuranceOnlyPayType(null, null)
        !new MapperOfBookingInitResponse.PayInfoBuilder().buildInsuranceOnlyPayType([], null)
        !new MapperOfBookingInitResponse.PayInfoBuilder().buildInsuranceOnlyPayType([null], null)
        !new MapperOfBookingInitResponse.PayInfoBuilder().buildInsuranceOnlyPayType([null], new PaymentMethodInfoType())
        !new MapperOfBookingInitResponse.PayInfoBuilder().buildInsuranceOnlyPayType([null, null], new PaymentMethodInfoType(status: "", unsupportedCorpXProductList: [null]))
        !new MapperOfBookingInitResponse.PayInfoBuilder().buildInsuranceOnlyPayType([null, new PaymentMethodInfoType(status: "DISABLED")], new PaymentMethodInfoType(status: "", unsupportedCorpXProductList: []))
        new MapperOfBookingInitResponse.PayInfoBuilder().buildInsuranceOnlyPayType([null, new PaymentMethodInfoType(status: "", unsupportedCorpXProductList: [null])], new PaymentMethodInfoType(status: "", unsupportedCorpXProductList: []))
    }

    def "buildHotelResourceToken"() {
        expect:
        new MapperOfBookingInitResponse.HotelResourceTokenBuilder().buildHotelResourceToken(new BookingInitAssembleRequest(resourceToken: new ResourceToken(), checkAvailInfo: Mock(WrapperOfCheckAvail.CheckAvailInfo)), null) != null
    }


    def "buildArriveTimeKey"() {
        expect:
        new MapperOfBookingInitResponse.HotelResourceTokenBuilder().buildArriveTimeKey(null) == null
        new MapperOfBookingInitResponse.HotelResourceTokenBuilder().buildArriveTimeKey(new BookingInitResponseType()) == null
        new MapperOfBookingInitResponse.HotelResourceTokenBuilder().buildArriveTimeKey(new BookingInitResponseType(bookingRoomInfo: new BookingRoomInfo())) == null
        new MapperOfBookingInitResponse.HotelResourceTokenBuilder().buildArriveTimeKey(new BookingInitResponseType(bookingRoomInfo: new BookingRoomInfo(arriveTimeInfo: new ArriveTimeInfo()))) == null
        new MapperOfBookingInitResponse.HotelResourceTokenBuilder().buildArriveTimeKey(new BookingInitResponseType(bookingRoomInfo: new BookingRoomInfo(arriveTimeInfo: new ArriveTimeInfo(arriveTimeDetailInfos: [])))) == null
        new MapperOfBookingInitResponse.HotelResourceTokenBuilder().buildArriveTimeKey(new BookingInitResponseType(bookingRoomInfo: new BookingRoomInfo(arriveTimeInfo: new ArriveTimeInfo(arriveTimeDetailInfos: [new ArriveTimeDetailInfo()])))) == null
    }

    def "buildGuaranteeAmountInfo"() {
        expect:
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildGuaranteeAmountInfo(null, null, null, null, null, null, null, null) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildGuaranteeAmountInfo(new GuaranteePriceType(), null, null, null, null, null, null, null) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildGuaranteeAmountInfo(new GuaranteePriceType(), [new GuaranteeMethodInfoType(guaranteeMethod: "ACCOUNT_GUARANTEE")], null, null, null, null, null, null) == null
        new MapperOfBookingInitResponse.PriceInfoBuilder().buildGuaranteeAmountInfo(new GuaranteePriceType(customGuaranteePrice: new PriceType(currency: "CNY", price: 2)), [new GuaranteeMethodInfoType(guaranteeMethod: "ACCOUNT_GUARANTEE")], null, null, null, null, null, null).amountInfo.customAmountInfo.currency == "CNY"
    }

    def "getKeyValueInfo"() {
        given:
        new MockUp<TokenParseUtil>() {
            @Mock
            public static <T> String generateToken(T pidEntity, Class<T> tClass) {
                return "token"
            }
        }
        expect:
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().getKeyValueInfo(new Remark(desc: "a")).text == "a"
    }

    def "buildRoomName"() {
        expect:
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildRoomName(new BookingInitRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(language: "zh-CN")), new RoomItem()).localeResourceName.locale == "zh-CN"
    }


    def "buildServiceFeeAttribute"() {
        expect:
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildServiceFeeAttribute(null, null, null) == null
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildServiceFeeAttribute(new ServiceChargeInfoType(), null, null) == null
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildServiceFeeAttribute(new ServiceChargeInfoType(chargeAmountPack: new BaseChargeAmount(chargeAmountCustomCurrency: new ServiceChargePriceType(amount: 0))), null, null) == null
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildServiceFeeAttribute(new ServiceChargeInfoType(chargeAmountPack: new BaseChargeAmount(chargeAmountCustomCurrency: new ServiceChargePriceType(amount: 1))), null, null).size() == 5
    }

    def "buildCancelPolicy returns null when cancelPolicyDescType is null"() {
        given:
        def builder = new MapperOfBookingInitResponse.BookingRoomInfoBuilder()

        when:
        def result = builder.buildCancelPolicy(null, Mock(GetSupportedPaymentMethodResponseType), [])

        then:
        result == null
    }

    def "buildTagInfos returns empty list when input is null"() {
        given:
        def builder = new MapperOfBookingInitResponse.BookingRoomInfoBuilder()

        when:
        def result = builder.buildTagInfos(null)

        then:
        result == null
    }

    def "buildTagInfos returns empty list when input list is empty"() {
        given:
        def builder = new MapperOfBookingInitResponse.BookingRoomInfoBuilder()

        when:
        def result = builder.buildTagInfos([])

        then:
        result == null
    }

    def "buildTagInfos maps valid input to TagAttributeInfo list"() {
        given:
        def builder = new MapperOfBookingInitResponse.BookingRoomInfoBuilder()
        def input = [
                new SaleRoomTagInfoType(tagCode: "code", configInfoList: [new ConfigInfoType(position: 1, style: 2, priority: 3)]),
        ]

        when:
        def result = builder.buildTagInfos(input)

        then:
        result.size() == 1
        result.tagAttributes.size() == 1
    }

    def "buildGuaranteePrice"() {
        expect:
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildGuaranteePrice(null) == null
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildGuaranteePrice(new CancelPolicyType()) == null
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildGuaranteePrice(new CancelPolicyType(guaranteePolicyInfo: new GuaranteeDetailType())) == null
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildGuaranteePrice(new CancelPolicyType(guaranteePolicyInfo: new GuaranteeDetailType(guaranteePriceInfo: new GuaranteePriceType()))) == null
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildGuaranteePrice(new CancelPolicyType(guaranteePolicyInfo: new GuaranteeDetailType(guaranteePriceInfo: new GuaranteePriceType(originGuaranteePrice: new PriceType())))) == null
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildGuaranteePrice(new CancelPolicyType(guaranteePolicyInfo: new GuaranteeDetailType(guaranteePriceInfo: new GuaranteePriceType(originGuaranteePrice: new PriceType(price: 2))))).amount == "2"
    }

    def "RoundTime"() {
        expect:
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().roundTime(LocalDateTime.of(2023, 2, 2, 15, 0, 34)).minute == 0
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().roundTime(LocalDateTime.of(2023, 2, 2, 15, 12, 34)).minute == 30
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().roundTime(LocalDateTime.of(2023, 2, 2, 15, 30, 34)).minute == 30
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().roundTime(LocalDateTime.of(2023, 2, 2, 15, 31, 34)).minute == 0
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().roundTime(LocalDateTime.of(2023, 2, 2, 15, 31, 34)).hour == 16
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().roundTime(LocalDateTime.of(2023, 2, 2, 15, 59, 34)).minute == 0
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().roundTime(LocalDateTime.of(2023, 2, 2, 15, 59, 34)).hour == 16
    }


    @Unroll
    def "GetStartTime"() {
        given:
        def earliestArriveTime = LocalDateTime.of(2023, 2, 2, 15, 0, 0)
        expect:
        def res = new MapperOfBookingInitResponse.BookingRoomInfoBuilder().getStartTime(earliestArriveTime, time)
        with(res) {
            res.hour == hour
            res.minute == minute
        }
        where:
        time                                    || hour | minute || desc
        LocalDateTime.of(2023, 2, 2, 14, 0, 0)  || 15   | 0      || "当前时间早于最早钟点房开始时间"
        LocalDateTime.of(2023, 2, 2, 14, 1, 0)  || 15   | 0      || "当前时间早于最早钟点房开始时间"
        LocalDateTime.of(2023, 2, 2, 13, 30, 0) || 15   | 0      || "当前时间早于最早钟点房开始时间"
        LocalDateTime.of(2023, 2, 2, 13, 59, 0) || 15   | 0      || "当前时间早于最早钟点房开始时间"
        LocalDateTime.of(2023, 2, 2, 16, 0, 0)  || 16   | 0      || "当前时间晚于最早钟点房开始时间"
        LocalDateTime.of(2023, 2, 2, 16, 30, 0) || 16   | 30     || "当前时间晚于最早钟点房开始时间"
        LocalDateTime.of(2023, 2, 2, 16, 31, 0) || 17   | 0      || "当前时间晚于最早钟点房开始时间"
        LocalDateTime.of(2023, 2, 2, 16, 59, 0) || 17   | 0      || "当前时间晚于最早钟点房开始时间"
        LocalDateTime.of(2023, 2, 2, 16, 1, 0)  || 16   | 30     || "当前时间晚于最早钟点房开始时间"

    }

    def "isMultiSel"() {
        expect:
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().isMultiSel(null) == false
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().isMultiSel([]) == false
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().isMultiSel([new RoomBedInfoType()]) == false
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().isMultiSel([new RoomBedInfoType(), null]) == false
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().isMultiSel([new RoomBedInfoType(bedGroupList: [null, null]), null]) == true
    }


    def "buildChildBedInfoType"() {
        expect:
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildChildBedInfoType(null) == null
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildChildBedInfoType(new SubBedInfoType(bedCount: 2)).bedCount == 2
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildChildBedInfoType(new SubBedInfoType(bedWidthRangeInfo: new BedWidthInfoType(bedWidthDesc: "a"))).bedWidthRangeInfo.bedWidthDesc == "a"
    }


    def "buildGiftInfo"() {
        given:
        def input = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        input.getRoomItem() >> new RoomItem(agreementGiftInfo: new AgreementGiftInfoType(giftToken: "a", giftDetailInfoList: [null, new GiftDetailInfoType(giftName: "b")]))
        expect:
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder.PackageInfoBuilder().buildGiftInfo(null) == null
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder.PackageInfoBuilder().buildGiftInfo(input).giftToken == "a"
    }


    def "buildPackageInfo"() {
        def input = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        input.getRoomItem() >> new RoomItem(packageRoomInfo: new PackageRoomInfoType(packageId: 2))

        expect:
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder.PackageInfoBuilder().buildPackageInfo(new BookingInitAssembleRequest()) == null
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder.PackageInfoBuilder().buildPackageInfo(new BookingInitAssembleRequest(checkAvailInfo: input, getPackageRoomListResponseType: new GetPackageRoomListResponseType())).packageId == 2
    }


    def "buildMembershipInfo"() {
        given:
        def input = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        input.getRoomItem() >> new RoomItem(bonusPointInfo: new BonusPointInfoType())
        def input2 = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        input2.getRoomItem() >> new RoomItem(bonusPointInfo: new BonusPointInfoType(bonusPointRoom: true), roomAttributes: new RoomAttributesType(onlyGroupMemberCanBook: true))
        expect:
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().buildMembershipInfo(null) == null
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().buildMembershipInfo(new BookingInitAssembleRequest()) == null
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().buildMembershipInfo(new BookingInitAssembleRequest(checkAvailInfo: input)) == null
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().buildMembershipInfo(new BookingInitAssembleRequest(checkAvailInfo: input2)).onlyGroupMemberCanBook == "T"
    }


    def "getCouponDetailInfo"() {
        expect:
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().getCouponDetailInfo(new CouponInfoType(groupName: "a", totalCustomAmount: new PriceType()), null, null).groupName == "a"
    }

    def "buildCardFillingRuleInfo"() {
        expect:
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().buildCardFillingRuleInfo(null, null, false).mandatory == "F"
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().buildCardFillingRuleInfo([new MemberBonusRuleEntry()], null, false).mandatory == "F"
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().buildCardFillingRuleInfo([new MemberBonusRuleEntry(groupId: 3)], 2, false).mandatory == "F"
        new MapperOfBookingInitResponse.AdditionalInfoBuilder().buildCardFillingRuleInfo([new MemberBonusRuleEntry(groupId: 2, inputPattern: "a")], 2, false).inputPattern == "a"
    }

    def "testIsPlatformRandomRoom"() {
        given:
        RanOfHouseInfoType ranOfHouseInfo = new RanOfHouseInfoType(ranOfHouse: ranOfHouse, type: type)
        expect:
        new MapperOfBookingInitResponse.BookingChangeInfoBuilder().isPlatformRandomRoom(ranOfHouseInfo) == result
        where:
        ranOfHouse | type    | result
        null       | null    | false
        true       | "hotel" | false
        true       | "platform" | true
        false       | "platform" | false
    }
    def "testBuildRandomRoomType"(){
        given:
        RanOfHouseInfoType ranOfHouseInfo = new RanOfHouseInfoType(ranOfHouse: ranOfHouse, type: type)
        expect:
        new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildRandomRoomType(ranOfHouseInfo)==result
        where:
        ranOfHouse | type    | result
        true       | null    | null
        true       | "hotel" | "hotel"
        true       | "platform" | "platform"
        false       | "platform" | null

    }

    def "测试 getCostCenterNew 方法应该正确返回成本中心值"() {
        given: "创建测试对象并设置 costCenterNew 值"
        def expectedCostCenter = "COST_CENTER_123"
        def request = BookingInitAssembleRequest.builder()
                .withCostCenterNew(expectedCostCenter)
                .build()

        when: "调用 getCostCenterNew 方法"
        def result = request.getCostCenterNew()

        then: "验证返回值与设置的值相匹配"
        result == expectedCostCenter
    }

    def "测试常量字段值是否正确"() {
        expect: "COST_CENTER常量应该等于'COST_CENTER'"
        ModuleCodeConstant.COST_CENTER == "COST_CENTER"
    }

    @Unroll
    def "测试needCostCenter方法 - 当isPrivate=#isPrivate, packageEnabled=#packageEnabled, tripNeedCostCenter=#tripNeedCostCenter时应该返回#expectedResult"() {
        given: "准备测试数据"
        def corpPayInfo = new CorpPayInfo(corpPayType: isPrivate ? "private" : "public")
        def bookingInitRequest = new BookingInitRequestType(
                corpPayInfo: corpPayInfo,
                integrationSoaRequestType: new IntegrationSoaRequestType(
                        userInfo: new UserInfo(corpId: "shanglv_001")
                )
        )
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)
        def strategyInfoMap = [:]

        def request = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(bookingInitRequest)
                .withAccountInfo(accountInfo)
                .withRoomPayType(HotelPayTypeEnum.CORP_PAY)
                .build()

        and: "Mock静态方法调用"
        GroovyMock(CorpPayInfoUtil, global: true)
        GroovyMock(StrategyOfBookingInitUtil, global: true)

        and: "设置mock行为"
        CorpPayInfoUtil.isPrivate(corpPayInfo) >> isPrivate
        accountInfo.isPackageEnabled() >> packageEnabled

        if (packageEnabled && !isPrivate) {
            StrategyOfBookingInitUtil.tripNeedCostCenter(strategyInfoMap) >> tripNeedCostCenter
        }

        when: "调用needCostCenter方法"
        def mapperResponse = new MapperOfBookingInitResponse()
        def moduleInfoBuilder = new MapperOfBookingInitResponse.ModuleInfoBuilder(mapperResponse)
        def result = moduleInfoBuilder.needCostCenter(request)

        then: "验证结果"
        result == expectedResult

        where: "测试用例数据"
        isPrivate | packageEnabled | tripNeedCostCenter | expectedResult | description
        true      | false         | false              | false          | "私有支付信息时返回false"
        true      | true          | false              | false          | "私有支付信息时返回false(packageEnabled=true)"
        true      | true          | true               | false          | "私有支付信息时返回false(packageEnabled=true, tripNeedCostCenter=true)"
        false     | true          | true               | false           | "非私有且启用套餐且需要成本中心"
        false     | true          | false              | false          | "非私有且启用套餐但不需要成本中心"
        false     | false         | false              | true           | "非私有且未启用套餐时返回true"
        false     | false         | true               | true           | "非私有且未启用套餐时返回true(tripNeedCostCenter值不影响)"
    }


    def "buildUseCertificateInfo"() {
        given:
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            private static CertificateInfo getDefaultCertificateInfo(List<CertificateInfo> certificateInfoList,
                                                                     WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
                return new CertificateInfo(certificateType: "passport")
            }
        }
        HotelBookPassengerInput orderCreateHotelPassengerInput = new HotelBookPassengerInput(
                certificateInfo: new CertificateInfo(certificateType: "ID_CARD"),
                certificateInfos: [new CertificateInfo(certificateType: "passport")],
        )
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo)
        when:
        def result = OrderCreateProcessorOfUtil.buildUseCertificateInfo(orderCreateHotelPassengerInput, checkAvailInfo)
        then:
        result != null
        result.certificateType == "passport"


        when:
        orderCreateHotelPassengerInput = new HotelBookPassengerInput(
                certificateInfo: new CertificateInfo(certificateType: "ID_CARD")
        )
        result = OrderCreateProcessorOfUtil.buildUseCertificateInfo(orderCreateHotelPassengerInput, checkAvailInfo)
        then:
        result != null
        result.certificateType == "ID_CARD"
    }

    def "getApprovalOutputByDefault"() {
        given:
        new MockUp<TokenInfoGetUtil>() {
            @Mock
            public static Boolean isOverSeaFromResourceToken(ResourceToken resourceToken) {
                return true
            }
        }
        ApprovalOutput approvalOutput = new ApprovalOutput()
        ApprovalInput approvalInput = new ApprovalInput(emergency: "T")
        BookingInitAssembleRequest request = Mock(BookingInitAssembleRequest) {
            getAccountInfo() >> Mock(WrapperOfAccount.AccountInfo) {
                isPreApprovalRequired(_, _) >> false
                isTravelApplyRequired(_, _) >> false
            }
            getBookingInitRequest() >> new BookingInitRequestType(corpPayInfo: new CorpPayInfo())
        }
        when:
        def result = new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildDefaultApprovalOutput(approvalOutput, approvalInput, false, request)
        then:
        result != null
        result.defaultEmergencyBook == "T"
    }


    def "preApproval"() {
        given:
        new MockUp<TokenInfoGetUtil>() {
            @Mock
            public static Boolean isOverSeaFromResourceToken(ResourceToken resourceToken) {
                return true
            }
        }
        ApprovalOutput approvalOutput = new ApprovalOutput()
        BatchApprovalDefaultResponseType approvalDefaultResponseType = new BatchApprovalDefaultResponseType()
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isPreApprovalRequired(_, _) >> false
            isTravelApplyRequired(_, _) >> false
        }
        when:
        def result = new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().preApproval(approvalOutput, "subApprovalNo", "masterApprovalNo", approvalDefaultResponseType, accountInfo, false, new CorpPayInfo(), null)
        then:
        result != null
        result.defaultEmergencyBook == "F"
        result.defaultApprovalMainNo == null
    }


    def "preApproval-isPreApprovalRequired"() {
        given:
        new MockUp<TokenInfoGetUtil>() {
            @Mock
            public static Boolean isOverSeaFromResourceToken(ResourceToken resourceToken) {
                return true
            }
        }
        ApprovalOutput approvalOutput = new ApprovalOutput()
        BatchApprovalDefaultResponseType approvalDefaultResponseType = new BatchApprovalDefaultResponseType()
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isPreApprovalRequired(_, _) >> true
            isTravelApplyRequired(_, _) >> false
        }
        when:
        def result = new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().preApproval(approvalOutput, "subApprovalNo", "masterApprovalNo", approvalDefaultResponseType, accountInfo, false, new CorpPayInfo(), null)
        then:
        result != null
        result.defaultEmergencyBook == "F"
        result.defaultApprovalMainNo == "masterApprovalNo"
    }

    def "buildTagInfos returns null when saleRoomTags is empty"() {
        given:
        def saleRoomTags = []

        when:
        def result = new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildTagInfos(saleRoomTags)

        then:
        result == null
    }

    def "buildTagInfos returns null when saleRoomTags is null"() {
        given:
        def saleRoomTags = null

        when:
        def result = new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildTagInfos(saleRoomTags)

        then:
        result == null
    }

    def "buildTagInfos returns list of TagInfo with attributes when saleRoomTags has valid data"() {
        given:
        def saleRoomTags = [
                Mock(SaleRoomTagInfoType) {
                    getTagCode() >> "Code1"
                    getTagName() >> "Name1"
                    getTagDesc() >> "Desc1"
                    getConfigInfoList() >> [
                            Mock(ConfigInfoType) {
                                getPosition() >> 1
                                getStyle() >> "Style1"
                                getColor() >> "Color1"
                                getPriority() >> 10
                            }
                    ]
                }
        ]

        when:
        def result = new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildTagInfos(saleRoomTags)

        then:
        result.size() == 1
        result[0].code == "Code1"
        result[0].name == "Name1"
        result[0].desc == "Desc1"
        result[0].tagAttributes.size() == 1
        result[0].tagAttributes[0].attributes.size() == 4
    }

    def "buildTagInfos returns list of TagInfo without attributes when configInfoList is empty"() {
        given:
        def saleRoomTags = [
                Mock(SaleRoomTagInfoType) {
                    getTagCode() >> "Code2"
                    getTagName() >> "Name2"
                    getTagDesc() >> "Desc2"
                    getConfigInfoList() >> []
                }
        ]

        when:
        def result = new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildTagInfos(saleRoomTags)

        then:
        result.size() == 1
        result[0].code == "Code2"
        result[0].name == "Name2"
        result[0].desc == "Desc2"
        result[0].tagAttributes == null
    }

    def "buildTagInfos filters out null saleRoomTags"() {
        given:
        def saleRoomTags = [
                null,
                Mock(SaleRoomTagInfoType) {
                    getTagCode() >> "Code3"
                    getTagName() >> "Name3"
                    getTagDesc() >> "Desc3"
                    getConfigInfoList() >> null
                }
        ]

        when:
        def result = new MapperOfBookingInitResponse.BookingRoomInfoBuilder().buildTagInfos(saleRoomTags)

        then:
        result.size() == 1
        result[0].code == "Code3"
        result[0].name == "Name3"
        result[0].desc == "Desc3"
        result[0].tagAttributes == null
    }


    def "buildModuleInfo - 只需要成本中心"() {
        given:
        def bookingInitRequest = Mock(BookingInitRequestType) {
            getCorpPayInfo() >> new CorpPayInfo("public")
        }
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isPackageEnabled() >> false
        }
        def request = Mock(BookingInitAssembleRequest) {
            getBookingInitRequest() >> bookingInitRequest
            getAccountInfo() >> accountInfo
        }
        def builder = new MapperOfBookingInitResponse.ModuleInfoBuilder()

        when:
        ModuleInfo result = builder.buildModuleInfo(request)

        then:
        result.moduleDetails.size() == 1
        result.moduleDetails[0].moduleCode == ModuleCodeConstant.COST_CENTER
    }

    def "buildModuleInfo - 只需要行程"() {
        given:
        def bookingInitRequest = Mock(BookingInitRequestType) {
            getCorpPayInfo() >> new CorpPayInfo("public")
        }
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isPackageEnabled() >> true
        }
        def request = Mock(BookingInitAssembleRequest) {
            getBookingInitRequest() >> bookingInitRequest
            getAccountInfo() >> accountInfo
        }

        def builder = Spy(MapperOfBookingInitResponse.ModuleInfoBuilder) {
            needCostCenter(_) >> false
            needTrip(_) >> true
        }

        when:
        ModuleInfo result = builder.buildModuleInfo(request)

        then:
        result.moduleDetails.size() == 1
        result.moduleDetails[0].moduleCode == ModuleCodeConstant.TRIP
    }

    def "buildModuleInfo - 两个都需要"() {
        given:
        def request = Mock(BookingInitAssembleRequest)
        def builder = Spy(MapperOfBookingInitResponse.ModuleInfoBuilder) {
            needCostCenter(_) >> true
            needTrip(_) >> true
        }

        when:
        ModuleInfo result = builder.buildModuleInfo(request)

        then:
        result.moduleDetails.size() == 2
        result.moduleDetails*.moduleCode.containsAll([ModuleCodeConstant.COST_CENTER, ModuleCodeConstant.TRIP])
    }

    def "buildModuleInfo - 都不需要"() {
        given:
        def request = Mock(BookingInitAssembleRequest)
        def builder = Spy(MapperOfBookingInitResponse.ModuleInfoBuilder) {
            needCostCenter(_) >> false
            needTrip(_) >> false
        }

        when:
        ModuleInfo result = builder.buildModuleInfo(request)

        then:
        result.moduleDetails.isEmpty()
    }

    @Unroll
    def "needTrip分支覆盖"() {
        given:
        def approvalFlowInput = Mock(ApprovalFlowInput) {
            getApprovalFlowOrderId() >> approvalFlowOrderId
        }
        def bookingInitRequest = Mock(BookingInitRequestType) {
            getCorpPayInfo() >> corpPayInfo
            getApprovalFlowInput() >> approvalFlowInput
        }
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isPackageEnabled() >> packageEnabled
        }
        def request = Mock(BookingInitAssembleRequest) {
            getBookingInitRequest() >> bookingInitRequest
            getAccountInfo() >> accountInfo
            getStrategyInfoMap() >> strategyInfoMap
        }

        // 静态mock CorpPayInfoUtil.isPrivate
        new MockUp<CorpPayInfoUtil>() {
            @Mock
            boolean isPrivate(Object arg) { isPrivate }
        }
        // 静态mock BookingInitUtil.approvalFlowReuseNew
        new MockUp<BookingInitUtil>() {
            @Mock
            boolean approvalFlowReuseNew(Object req, Object map) { approvalFlowReuseNew }
        }

        def builder = new MapperOfBookingInitResponse.ModuleInfoBuilder()

        expect:
        builder.needTrip(request) == expected

        where:
        isPrivate | packageEnabled | approvalFlowReuseNew | approvalFlowOrderId | expected | desc
        false     | false          | false                | null                | false    | "未启用套餐，直接false"
        false     | true           | true                 | null                | true     | "审批沿用新版，true"
        false     | true           | false                | "orderId"           | false    | "有审批单号，false"
        false     | true           | false                | null                | true     | "其它情况，true"
        corpPayInfo = new CorpPayInfo("public")
        strategyInfoMap = [:]
    }





    @Unroll
    def "testBuildInitConfigInfoTravelStandPolicy with #description"() {
        given: "A mocked AccountInfo"
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isTravelStandPolicy() >> travelStandPolicy
        }

        when: "Calling buildInitConfigInfoTravelStandPolicy"
        def result = new MapperOfBookingInitResponse.BookingConfigInfoBuilder().buildInitConfigInfoTravelStandPolicy(accountInfo)

        then: "The result should match the expected InitConfigInfo"
        result.configKey == InitConfigInfoConstant.TRAVEL_STAND_POLICY
        result.configValue == expectedValue

        where:
        description                     | travelStandPolicy | expectedValue
        "accountInfo is null"           | false             | "F"
        "travelStandPolicy is enabled"  | true              | "T"
        "travelStandPolicy is disabled" | false             | "F"
    }

// ... existing code ...
    def "buildXProductQueryBOMarketingSearchRequestParam"() {
        given:
        def builder = new MapperOfBookingInitResponse.AdditionalInfoBuilder()

        // 创建所需的参数对象
        def bookingInitRequestType = new BookingInitRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(
                        sourceFrom: "H5",
                        language: "zh-CN",
                        requestId: "test-session-id",
                        userInfo: new UserInfo(
                                corpId: "test-corp-id",
                                userId: "test-user-id"
                        )
                ),
                corpPayInfo: new CorpPayInfo(corpPayType: "public"),
                hotelBookInput: new HotelBookInput(
                        hotelDateRangeInfo: new HotelDateRangeInfo(
                                checkIn: "2025-10-01",
                                checkOut: "2025-10-03"
                        )
                )
        )

        def checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        def roomItem = Mock(RoomItem)
        roomItem.getXProductTokenList() >> ["token1", "token2"]
        checkAvailInfo.getRoomItem() >> roomItem

        def resourceToken = new ResourceToken(
                hotelResourceToken: Mock(HotelResourceToken) {
                    getHotelId() >> 5678
                    getHotelGeoInfoResourceToken() >> Mock(HotelGeoInfoResourceToken) {
                        getCityId() >> 1234
                    }
                },
                roomResourceToken: new RoomResourceToken(
                        balanceType: "PP",
                        roomType: "D"
                )
        )

        def accountInfo = Mock(WrapperOfAccount.AccountInfo)
        def roomPayType = HotelPayTypeEnum.CORP_PAY

        and: "Mock静态方法"
        GroovyMock(CorpPayInfoUtil, global: true)
        CorpPayInfoUtil.isPublic(_) >> true

        new MockUp<CityInfoUtil>() {
            @Mock
            boolean oversea(Integer cityId) {
                return false
            }
        }

        when:
        def result = builder.buildXProductQueryBOMarketingSearchRequestParam(
                bookingInitRequestType,
                checkAvailInfo,
                resourceToken,
                accountInfo,
                roomPayType
        )

        then:
        result != null
        result.requestHeader != null
        result.requestHeader.channel == "APP"
        result.requestHeader.language == "zh-CN"
        result.requestHeader.sessionId == "test-session-id"
        result.feeType == "C"
        result.searchAccount != null
        result.searchAccount.corpId == "test-corp-id"
        result.searchAccount.uid == "test-user-id"
        result.queryCondition != null
        result.queryCondition.marketingBizType == "DOMESTICHOTEL"
        result.queryCondition.pageTypeCode == "BookingFill"
        result.XProductTokenList != null
        result.XProductTokenList.size() == 2
        result.XProductTokenList.contains("token1")
        result.XProductTokenList.contains("token2")
    }
// ... existing code ...



    def "testBuildCheckOutApprovalDateRange"(){
        when:
        new MockUp<CorpCustomizeUtil>(){
            @Mock
            public static boolean isGwCorp(String corpId) {
                return "gw".equalsIgnoreCase(corpId);
            }
        }
        then:
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildCheckInApprovalDateRange(null, null).getBeginDate()==null
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildCheckOutApprovalDateRange(null, null,null).getBeginDate()==null
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildCheckOutApprovalDateRange("2025-07-29", "2025-08-01",null).getBeginDate()=="2025-07-29"
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildCheckOutApprovalDateRange("2025-07-29", "2025-08-01","gw").getEndDate()=="2025-08-02"

    }

    def "notSupportInsuranceInfo1 with #description"() {
        given: "A mocked QConfigOfCustomConfig and a BookingInitAssembleRequest"
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                return "shanglv_002".equalsIgnoreCase(corpId)
            }
        }

        def strategyInfoMap = strategyInfoMapInput
        def request = new BookingInitAssembleRequest(
                strategyInfoMap: strategyInfoMap,
                bookingInitRequest: new BookingInitRequestType(
                        integrationSoaRequestType: new IntegrationSoaRequestType(
                                userInfo: new UserInfo(corpId: corpIdTest, userId: "_SL2240552768")
                        )
                )
        )

        when: "Calling notSupportInsuranceInfo"
        def result = new MapperOfBookingInitResponse.AdditionalInfoBuilder().notSupportInsuranceInfo(request)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                | corpIdTest    | isSupportExtendStay | strategyInfoMapInput                                                                                   || expectedResult
        "isSupportExtendStay is false"             | "shanglv_001" | false               | ["BOOKING_SCENARIO": new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "EXTEND")]       || true

        "isSupportExtendStay is true"              | "shanglv_002" | true                | ["BOOKING_SCENARIO": new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MODIFY")]       || false
        "isSupportExtendStay is true APPLY_MODIFY" | "shanglv_002" | true                | ["BOOKING_SCENARIO": new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "APPLY_MODIFY")] || true
        "strategyInfoMap is null"                  | "shanglv_002" | true                | null                                                                                                   || false
        "strategyInfoMap contains EXTEND value"    | "shanglv_003" | false               | ["BOOKING_SCENARIO": new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "APPLY_MODIFY")] || true
        "strategyInfoMap contains EXTEND value1"   | "shanglv_002" | true                | ["BOOKING_SCENARIO": new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "EXTEND")]       || false
        "strategyInfoMap contains MODIFY value"    | "shanglv_002" | true                | ["BOOKING_SCENARIO": new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MODIFY")]       || false
        "strategyInfoMap contains unrelated key"   | "shanglv_002" | true                | ["BOOKING_SCENARIO": new StrategyInfo(strategyKey: "OTHER_KEY", strategyValue: "OTHER_VALUE")]         || false

    }

    def "buildSupportApprovalModel" () {
        given:
        def isPreApprovalRequired = Mock(WrapperOfAccount.AccountInfo) {
            isPreApprovalRequired(_, _) >> true
        }
        def isTravelApplyRequired = Mock(WrapperOfAccount.AccountInfo) {
            isPreApprovalRequired(_, _) >> false
            isTravelApplyRequired(_, _) >> true
        }

        def preApprovalStrategyByPsg = Mock(WrapperOfAccount.AccountInfo) {
            isPreApprovalRequired(_, _) >> false
            isTravelApplyRequired(_, _) >> false
            preApprovalStrategyByPsg(_, _) >> true
        }
        def preApprovalChooseByPsg = Mock(WrapperOfAccount.AccountInfo) {
            isPreApprovalRequired(_, _) >> false
            isTravelApplyRequired(_, _) >> false
            preApprovalStrategyByPsg(_, _) >> false
            preApprovalChooseByPsg(_, _) >> true
        }

        when:
        expect:
        !new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildSupportApprovalModel(null, new CorpPayInfo(corpPayType: "private"), null, null)
        !new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildSupportApprovalModel(null, new CorpPayInfo(corpPayType: "public"), null, null)
        !new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildSupportApprovalModel(null, new CorpPayInfo(corpPayType: "public"), Mock(WrapperOfAccount.AccountInfo), null)
        !new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildSupportApprovalModel(new MiceInput(miceToken: "a"), new CorpPayInfo(corpPayType: "public"), Mock(WrapperOfAccount.AccountInfo), null)
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildSupportApprovalModel(null, new CorpPayInfo(corpPayType: "public"), isPreApprovalRequired, null)
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildSupportApprovalModel(null, new CorpPayInfo(corpPayType: "public"), isTravelApplyRequired, null)
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildSupportApprovalModel(null, new CorpPayInfo(corpPayType: "public"), preApprovalStrategyByPsg, null)
        new MapperOfBookingInitResponse.CorpSpecificInfoBuilder().buildSupportApprovalModel(null, new CorpPayInfo(corpPayType: "public"), preApprovalChooseByPsg, null)

    }
    def "testBuildChangeInfos"(){
        MapperOfBookingInitResponse.BookingChangeInfoBuilder builder = new MapperOfBookingInitResponse.BookingChangeInfoBuilder()
        when:
        new MockUp<MealUtil>(){
            @Mock
            public static String getRoomMealTip(Integer mealType, List<DailyMealInfo> dailyMealInfoList) {
                return "2份早餐"
            }
        }
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem( balanceType: "PP"))))
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        BookingInitAssembleRequest request = new BookingInitAssembleRequest(
                bookingInitRequest: new BookingInitRequestType(
                        integrationSoaRequestType: new IntegrationSoaRequestType(
                                userInfo: new UserInfo(corpId: "shanglv_001", userId: "_SL2240552768")
                        )
                ),
                checkAvailInfo: checkAvailInfo,
                cancelPolicyDesc:new CancelPolicyDescType(
                        titleWithTime: "不可取消"
                ),
                accountInfo: Mock(WrapperOfAccount.AccountInfo) {
                    isPackageEnabled() >> true
                },
                resourceToken: new ResourceToken(
                        hotelResourceToken: new HotelResourceToken(hotelId: 1234, hotelGeoInfoResourceToken: new HotelGeoInfoResourceToken(cityId: 5678)),
                        roomResourceToken: new RoomResourceToken(balanceType: "PP", roomType: "D",cancelPolicyDesc: "免费取消",breakfastInfo: "1份早餐",confirmType: ConfirmTypeEnum.IMMEDIATE)
                ),
        )
        List<BookingChangeInfo> result =  new MapperOfBookingInitResponse.BookingChangeInfoBuilder(new MapperOfBookingInitResponse()).buildChangeInfos(request)
        then:
        Assert.assertEquals(3, result.size())
    }

    def "test buildInsuranceInvoiceTipInfo when new invoice supported"() {
        given:
        new MockUp<BookingInitUtil>() {
            @Mock
            boolean supportInvoiceTip(BookingInitRequestType bookingInitRequestType,
                                      GetSupportedInvoiceTypeResponseType getSupportedInvoiceTypeResponseType) {
                return false
            }

            @Mock
            boolean supportNewInvoice(BookingInitRequestType bookingInitRequestType) {
                return true
            }
        }

        BookingInitAssembleRequest request = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType())
                .withGetSupportedInvoiceTypeResponseType(new GetSupportedInvoiceTypeResponseType())
                .build()

        InvoiceTip expected = new InvoiceTip()
        new MockUp<MapperOfBookingInitResponse.InvoiceTipInfoBuilder>() {
            @Mock
            InvoiceTip buildInsuranceFeeInvoiceTips(BookingInitAssembleRequest assembleRequest) {
                return expected
            }
        }

        when:
        InvoiceTip result = new MapperOfBookingInitResponse.InvoiceTipInfoBuilder().buildInsuranceInvoiceTipInfo(request)

        then:
        result == expected
    }

    // 辅助方法：创建 ApprovalInput 对象
    private ApprovalInput createApprovalInput(String subApprovalNo, String emergency) {
        def input = new ApprovalInput()
        input.setSubApprovalNo(subApprovalNo)
        input.setEmergency(emergency)
        return input
    }

    // 辅助方法：创建 ApprovalOutput 对象
    private ApprovalOutput createApprovalOutput(String approvalSubNo, String emergencyBook) {
        def output = new ApprovalOutput()
        output.setApprovalSubNo(approvalSubNo)
        output.setEmergencyBook(emergencyBook)
        return output
    }

    // 辅助方法：创建 ApprovalInfoBO 对象
    private ApprovalInfoBO createApprovalInfoBO(String subApprovalNo, String emergency) {
        def bo = new ApprovalInfoBO()
        bo.setSubApprovalNo(subApprovalNo)
        bo.setEmergency(emergency)
        return bo
    }

    /**
     * 测试 buildApprovalInfoBO 方法
     * 根据 firstRequest 策略和输入参数构建 ApprovalInfoBO 对象
     */
    @Unroll
    def "test buildApprovalInfoBO - #description"() {
        given: "准备测试数据和Mock"
        def mapper = new MapperOfBookingInitResponse()

        // Mock StrategyOfBookingInitUtil.firstRequest 方法
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            public static boolean firstRequest(List<StrategyInfo> strategyInfos) {
                return isFirstRequest
            }
        }

        // Mock BooleanUtil.parseStr 方法
        new MockUp<BooleanUtil>() {
            @Mock
            public static String parseStr(boolean value) {
                return value ? "T" : "F"
            }
        }

        def bookingInitRequestType = new BookingInitRequestType()
        bookingInitRequestType.setStrategyInfos(strategyInfos)
        def request = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(bookingInitRequestType)
                .withApprovalInput(approvalInput)
                .withApprovalOutput(approvalOutput)
                .build()

        when: "调用 buildApprovalInfoBO 方法"
        def result = mapper.buildApprovalInfoBO(request)

        then: "验证结果符合预期"
        if (expectedResult == null) {
            assert result == null
        } else {
            assert result != null
            assert result.subApprovalNo == expectedResult.subApprovalNo
            assert result.emergency == expectedResult.emergency
        }

        where:
        description                                    | isFirstRequest | strategyInfos                                                                                                                              | approvalInput                                                                                                          | approvalOutput                                                                                                         | expectedResult
        "firstRequest=false, approvalInput=null"      | false          | [new StrategyInfo(strategyKey: "FIRST_REQUEST", strategyValue: "F")]                                                                       | null                                                                                                                   | null                                                                                                                   | null
        "firstRequest=false, approvalInput with subApprovalNo" | false          | [new StrategyInfo(strategyKey: "FIRST_REQUEST", strategyValue: "F")]                                                                       | createApprovalInput("SUB123", "F")                                                                                    | null                                                                                                                   | createApprovalInfoBO("SUB123", null)
        "firstRequest=false, approvalInput with emergency" | false          | [new StrategyInfo(strategyKey: "FIRST_REQUEST", strategyValue: "F")]                                                                       | createApprovalInput("", "T")                                                                                          | null                                                                                                                   | createApprovalInfoBO(null, "T")
        "firstRequest=false, approvalInput with both" | false          | [new StrategyInfo(strategyKey: "FIRST_REQUEST", strategyValue: "F")]                                                                       | createApprovalInput("SUB456", "T")                                                                                    | null                                                                                                                   | createApprovalInfoBO("SUB456", "T")
        "firstRequest=false, approvalInput empty values" | false          | [new StrategyInfo(strategyKey: "FIRST_REQUEST", strategyValue: "F")]                                                                       | createApprovalInput("", "F")                                                                                          | null                                                                                                                   | createApprovalInfoBO(null, null)
        "firstRequest=true, approvalOutput=null"      | true           | [new StrategyInfo(strategyKey: "FIRST_REQUEST", strategyValue: "T")]                                                                       | null                                                                                                                   | null                                                                                                                   | null
        "firstRequest=true, approvalOutput with approvalSubNo" | true           | [new StrategyInfo(strategyKey: "FIRST_REQUEST", strategyValue: "T")]                                                                       | null                                                                                                                   | createApprovalOutput("APPROVAL789", "F")                                                                              | createApprovalInfoBO("APPROVAL789", null)
        "firstRequest=true, approvalOutput with emergencyBook" | true           | [new StrategyInfo(strategyKey: "FIRST_REQUEST", strategyValue: "T")]                                                                       | null                                                                                                                   | createApprovalOutput("", "T")                                                                                         | createApprovalInfoBO(null, "T")
        "firstRequest=true, approvalOutput with both" | true           | [new StrategyInfo(strategyKey: "FIRST_REQUEST", strategyValue: "T")]                                                                       | null                                                                                                                   | createApprovalOutput("APPROVAL999", "T")                                                                              | createApprovalInfoBO("APPROVAL999", "T")
        "firstRequest=true, approvalOutput empty values" | true           | [new StrategyInfo(strategyKey: "FIRST_REQUEST", strategyValue: "T")]                                                                       | null                                                                                                                   | createApprovalOutput("", "F")                                                                                         | createApprovalInfoBO(null, null)
    }

    /**
     * 测试 buildApprovalInfoBO 方法的边界情况
     * 包括 null 值处理和特殊字符串处理
     */
    @Unroll
    def "test buildApprovalInfoBO edge cases - #description"() {
        given: "准备边界情况测试数据"
        def mapper = new MapperOfBookingInitResponse()

        // Mock StrategyOfBookingInitUtil.firstRequest 方法
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            public static boolean firstRequest(List<StrategyInfo> strategyInfos) {
                return isFirstRequest
            }
        }

        // Mock BooleanUtil.parseStr 方法
        new MockUp<BooleanUtil>() {
            @Mock
            public static String parseStr(boolean value) {
                return value ? "T" : "F"
            }
        }

        def request = requestInput

        when: "调用 buildApprovalInfoBO 方法"
        def result = mapper.buildApprovalInfoBO(request)

        then: "验证结果符合预期"
        if (expectedResult == null) {
            assert result == null
        } else {
            assert result != null
            assert result.subApprovalNo == expectedResult.subApprovalNo
            assert result.emergency == expectedResult.emergency
        }

        where:
        description                                    | isFirstRequest | requestInput                                                                                                                               | expectedResult
        "request is null"                              | false          | null                                                                                                                                       | null
        "bookingInitRequest is null"                   | false          | BookingInitAssembleRequest.builder().build()                                                                                              | null
        "strategyInfos is null"                        | false          | BookingInitAssembleRequest.builder().withBookingInitRequest(new BookingInitRequestType()).build()                                       | null
        "strategyInfos is empty list"                  | false          | BookingInitAssembleRequest.builder().withBookingInitRequest(new BookingInitRequestType(strategyInfos: [])).build()                     | null
        "firstRequest=false, approvalInput with null subApprovalNo" | false          | createRequestWithApprovalInput(null, "T") | createApprovalInfoBO(null, "T")
        "firstRequest=false, approvalInput with whitespace subApprovalNo" | false          | createRequestWithApprovalInput("   ", "F") | createApprovalInfoBO(null, null)
        "firstRequest=true, approvalOutput with null approvalSubNo" | true           | createRequestWithApprovalOutput(null, "T") | createApprovalInfoBO(null, "T")
        "firstRequest=true, approvalOutput with whitespace approvalSubNo" | true           | createRequestWithApprovalOutput("   ", "F") | createApprovalInfoBO(null, null)
    }

    /**
     * 测试 buildApprovalInfoBO 方法中 BooleanUtil.parseStr(true) 的逻辑
     * 验证 emergency 字段的布尔值比较逻辑
     */
    @Unroll
    def "test buildApprovalInfoBO emergency boolean logic - #description"() {
        given: "准备布尔值测试数据"
        def mapper = new MapperOfBookingInitResponse()

        // Mock StrategyOfBookingInitUtil.firstRequest 方法
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            public static boolean firstRequest(List<StrategyInfo> strategyInfos) {
                return isFirstRequest
            }
        }

        // Mock BooleanUtil.parseStr 方法
        new MockUp<BooleanUtil>() {
            @Mock
            public static String parseStr(boolean value) {
                return value ? "T" : "F"
            }
        }

        def request = BookingInitAssembleRequest.builder()
                .withBookingInitRequest(new BookingInitRequestType(strategyInfos: []))
                .withApprovalInput(approvalInput)
                .withApprovalOutput(approvalOutput)
                .build()

        when: "调用 buildApprovalInfoBO 方法"
        def result = mapper.buildApprovalInfoBO(request)

        then: "验证 emergency 字段的处理逻辑"
        if (expectedEmergency == null) {
            assert result == null || result.emergency == null
        } else {
            assert result != null
            assert result.emergency == expectedEmergency
        }

        where:
        description                                    | isFirstRequest | approvalInput                                                                                                          | approvalOutput                                                                                                         | expectedEmergency
        "firstRequest=false, emergency='T'"           | false          | new ApprovalInput(emergency: "T")                                                                                     | null                                                                                                                   | "T"
        "firstRequest=false, emergency='t'"           | false          | new ApprovalInput(emergency: "t")                                                                                     | null                                                                                                                   | "t"
        "firstRequest=false, emergency='TRUE'"        | false          | new ApprovalInput(emergency: "TRUE")                                                                                  | null                                                                                                                   | null
        "firstRequest=false, emergency='F'"           | false          | new ApprovalInput(emergency: "F")                                                                                     | null                                                                                                                   | null
        "firstRequest=false, emergency='false'"       | false          | new ApprovalInput(emergency: "false")                                                                                 | null                                                                                                                   | null
        "firstRequest=false, emergency=null"          | false          | new ApprovalInput(emergency: null)                                                                                    | null                                                                                                                   | null
        "firstRequest=true, emergencyBook='T'"        | true           | null                                                                                                                   | new ApprovalOutput(emergencyBook: "T")                                                                                | "T"
        "firstRequest=true, emergencyBook='t'"        | true           | null                                                                                                                   | new ApprovalOutput(emergencyBook: "t")                                                                                | "t"
        "firstRequest=true, emergencyBook='TRUE'"     | true           | null                                                                                                                   | new ApprovalOutput(emergencyBook: "TRUE")                                                                             | null
        "firstRequest=true, emergencyBook='F'"        | true           | null                                                                                                                   | new ApprovalOutput(emergencyBook: "F")                                                                                | null
        "firstRequest=true, emergencyBook=null"       | true           | null                                                                                                                   | new ApprovalOutput(emergencyBook: null)                                                                               | null
    }

}
