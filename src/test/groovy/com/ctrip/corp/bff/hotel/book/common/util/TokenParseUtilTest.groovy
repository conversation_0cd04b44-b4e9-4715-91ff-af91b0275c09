package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BookResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.common.RcToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ResourceTokenInfo
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2024/11/7 22:23
 *
 */
class TokenParseUtilTest extends Specification {


    def savePoint = new SavePoint()

    void setup() {
        new MockUp<LogUtil>() {
            @Mock
            public static void loggingClogOnly(LogLevelEnum level,
                                               Class<?> type,
                                               String title,
                                               String message,
                                               Map<String, String> indexTags) {
                return;
            }
            @Mock
            public static void loggingClogOnly(LogLevelEnum level,
                                               Class<?> type,
                                               String title,
                                               String message,
                                               Throwable throwable,
                                               Map<String, String> indexTags) {
                return;
            }
        }
        new MockUp<JsonUtil>() {
            @Mock
            public static String toJson(Object obj) {
                return "mock";
            }
        }
    }

    void cleanup() {
        savePoint.rollback()
    }
    def "test OrderCreateToken"() {
        given:
        when:
        def result = TokenParseUtil.parseToken("H4sIAAAAAAAAAOMSCfb0c/dxjXcMCAjyD3P0iXfz8Q9X0uBSMUwzT0qxTDXWNbQ0NtQ1MTYw0E1MNU/WTUq2MEpLNrY0tTQwEWDQYAAA20jsikIAAAA\\u003d", OrderCreateToken.class)
        def result2 = TokenParseUtil.parseToken("H4sIAAAAAAAA_-Pi9fRz8_d0iQ9y9fX0c9FgBAD_0xXNEQAAAA", OrderCreateToken.class)
        then:
        result != null
        result2 != null
    }

    def "test RcToken"() {
        given:
        when:
        def result = TokenParseUtil.parseToken("H4sIAAAAAAAAABNicnGREnnaP-3Zto6Xi1perFsU5Pxkx9pn09qVGN0AXJrx9B0AAAA", RcToken.class)
        then:
        result != null
    }

    def "test parseResourceToken - null resourceTokenInfo"() {
        when:
        TokenParseUtil.parseResourceToken(null)

        then:
        def exception = thrown(BusinessException)
        exception.errorCode == OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_ERROR.errorCode
        exception.message == "resourceToken error"
    }

    def "test parseResourceToken - resourceTokenInfo with null token"() {
        given:
        def resourceTokenInfo = new ResourceTokenInfo()
        resourceTokenInfo.setResourceToken(null)

        when:
        TokenParseUtil.parseResourceToken(resourceTokenInfo)

        then:
        def exception = thrown(BusinessException)
        exception.errorCode == OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_ERROR.errorCode
        exception.message == "resourceToken error"
    }

    def "test parseResourceToken - resourceTokenInfo with blank token"() {
        given:
        def resourceTokenInfo = new ResourceTokenInfo()
        resourceTokenInfo.setResourceToken("")

        when:
        TokenParseUtil.parseResourceToken(resourceTokenInfo)

        then:
        def exception = thrown(BusinessException)
        exception.errorCode == OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_ERROR.errorCode
        exception.message == "resourceToken error"
    }

    def "test parseResourceToken - resourceTokenInfo with whitespace token"() {
        given:
        def resourceTokenInfo = new ResourceTokenInfo()
        resourceTokenInfo.setResourceToken("   ")

        when:
        TokenParseUtil.parseResourceToken(resourceTokenInfo)

        then:
        def exception = thrown(BusinessException)
        exception.errorCode == OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_ERROR.errorCode
        exception.message == "resourceToken error"
    }

    def "test parseResourceToken - invalid token that cannot be parsed"() {
        given:
        def resourceTokenInfo = new ResourceTokenInfo()
        resourceTokenInfo.setResourceToken("invalidToken")

        when:
        TokenParseUtil.parseResourceToken(resourceTokenInfo)

        then:
        def exception = thrown(BusinessException)
        exception.errorCode == OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_ERROR.errorCode
        exception.message == "resourceToken error"
    }

    def "test parseResourceToken - valid token but null ReservationResourceToken"() {
        given:
        def resourceTokenInfo = new ResourceTokenInfo()
        def resourceToken = new ResourceToken()
        def tokenStr = TokenParseUtil.generateToken(resourceToken, ResourceToken.class)
        resourceTokenInfo.setResourceToken(tokenStr)

        when:
        TokenParseUtil.parseResourceToken(resourceTokenInfo)

        then:
        def exception = thrown(BusinessException)
        exception.errorCode == OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_ERROR.errorCode
        exception.message == "resourceToken error"
    }

    def "test parseResourceToken - valid token but ReservationResourceToken with null wsId"() {
        given:
        def resourceTokenInfo = new ResourceTokenInfo()
        def reservationResourceToken = new ReservationResourceToken()
        def resourceToken = new ResourceToken()
        resourceToken.setReservationResourceToken(reservationResourceToken)
        def tokenStr = TokenParseUtil.generateToken(resourceToken, ResourceToken.class)
        resourceTokenInfo.setResourceToken(tokenStr)

        when:
        TokenParseUtil.parseResourceToken(resourceTokenInfo)

        then:
        def exception = thrown(BusinessException)
        exception.errorCode == OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_ERROR.errorCode
        exception.message == "resourceToken error"
    }

    def "test parseResourceToken - valid token but ReservationResourceToken with blank wsId"() {
        given:
        def resourceTokenInfo = new ResourceTokenInfo()
        def reservationResourceToken = new ReservationResourceToken()
        reservationResourceToken.setWsId("")
        def resourceToken = new ResourceToken()
        resourceToken.setReservationResourceToken(reservationResourceToken)
        def tokenStr = TokenParseUtil.generateToken(resourceToken, ResourceToken.class)
        resourceTokenInfo.setResourceToken(tokenStr)

        when:
        TokenParseUtil.parseResourceToken(resourceTokenInfo)

        then:
        def exception = thrown(BusinessException)
        exception.errorCode == OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_ERROR.errorCode
        exception.message == "resourceToken error"
    }

    def "test parseResourceToken - valid token but ReservationResourceToken with whitespace wsId"() {
        given:
        def resourceTokenInfo = new ResourceTokenInfo()
        def reservationResourceToken = new ReservationResourceToken()
        reservationResourceToken.setWsId("   ")
        def resourceToken = new ResourceToken()
        resourceToken.setReservationResourceToken(reservationResourceToken)
        def tokenStr = TokenParseUtil.generateToken(resourceToken, ResourceToken.class)
        resourceTokenInfo.setResourceToken(tokenStr)

        when:
        TokenParseUtil.parseResourceToken(resourceTokenInfo)

        then:
        def exception = thrown(BusinessException)
        exception.errorCode == OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_ERROR.errorCode
        exception.message == "resourceToken error"
    }

    def "test parseResourceToken - valid wsId but null BookResourceToken"() {
        given:
        def resourceTokenInfo = new ResourceTokenInfo()
        def reservationResourceToken = new ReservationResourceToken()
        reservationResourceToken.setWsId("validWsId")
        def resourceToken = new ResourceToken()
        resourceToken.setReservationResourceToken(reservationResourceToken)
        def tokenStr = TokenParseUtil.generateToken(resourceToken, ResourceToken.class)
        resourceTokenInfo.setResourceToken(tokenStr)

        when:
        TokenParseUtil.parseResourceToken(resourceTokenInfo)

        then:
        def exception = thrown(BusinessException)
        exception.errorCode == OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_ERROR.errorCode
        exception.message == "resourceToken error"
    }

    def "test parseResourceToken - valid wsId but BookResourceToken with null bookInfoStr"() {
        given:
        def resourceTokenInfo = new ResourceTokenInfo()
        def reservationResourceToken = new ReservationResourceToken()
        reservationResourceToken.setWsId("validWsId")
        def bookResourceToken = new BookResourceToken()
        def resourceToken = new ResourceToken()
        resourceToken.setReservationResourceToken(reservationResourceToken)
        resourceToken.setBookResourceToken(bookResourceToken)
        def tokenStr = TokenParseUtil.generateToken(resourceToken, ResourceToken.class)
        resourceTokenInfo.setResourceToken(tokenStr)

        when:
        TokenParseUtil.parseResourceToken(resourceTokenInfo)

        then:
        def exception = thrown(BusinessException)
        exception.errorCode == OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_ERROR.errorCode
        exception.message == "resourceToken error"
    }

    def "test parseResourceToken - valid wsId but BookResourceToken with blank bookInfoStr"() {
        given:
        def resourceTokenInfo = new ResourceTokenInfo()
        def reservationResourceToken = new ReservationResourceToken()
        reservationResourceToken.setWsId("validWsId")
        def bookResourceToken = new BookResourceToken()
        bookResourceToken.setBookInfoStr("")
        def resourceToken = new ResourceToken()
        resourceToken.setReservationResourceToken(reservationResourceToken)
        resourceToken.setBookResourceToken(bookResourceToken)
        def tokenStr = TokenParseUtil.generateToken(resourceToken, ResourceToken.class)
        resourceTokenInfo.setResourceToken(tokenStr)

        when:
        TokenParseUtil.parseResourceToken(resourceTokenInfo)

        then:
        def exception = thrown(BusinessException)
        exception.errorCode == OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_ERROR.errorCode
        exception.message == "resourceToken error"
    }

    def "test parseResourceToken - valid wsId but BookResourceToken with whitespace bookInfoStr"() {
        given:
        def resourceTokenInfo = new ResourceTokenInfo()
        def reservationResourceToken = new ReservationResourceToken()
        reservationResourceToken.setWsId("validWsId")
        def bookResourceToken = new BookResourceToken()
        bookResourceToken.setBookInfoStr("   ")
        def resourceToken = new ResourceToken()
        resourceToken.setReservationResourceToken(reservationResourceToken)
        resourceToken.setBookResourceToken(bookResourceToken)
        def tokenStr = TokenParseUtil.generateToken(resourceToken, ResourceToken.class)
        resourceTokenInfo.setResourceToken(tokenStr)

        when:
        TokenParseUtil.parseResourceToken(resourceTokenInfo)

        then:
        def exception = thrown(BusinessException)
        exception.errorCode == OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_ERROR.errorCode
        exception.message == "resourceToken error"
    }

    def "test parseResourceToken - success case"() {
        given:
        def resourceTokenInfo = new ResourceTokenInfo()
        def reservationResourceToken = new ReservationResourceToken()
        reservationResourceToken.setWsId("validWsId")
        def bookResourceToken = new BookResourceToken()
        bookResourceToken.setBookInfoStr("validBookInfoStr")
        def resourceToken = new ResourceToken()
        resourceToken.setReservationResourceToken(reservationResourceToken)
        resourceToken.setBookResourceToken(bookResourceToken)
        def tokenStr = TokenParseUtil.generateToken(resourceToken, ResourceToken.class)
        resourceTokenInfo.setResourceToken(tokenStr)

        when:
        def result = TokenParseUtil.parseResourceToken(resourceTokenInfo)

        then:
        result != null
        result.getReservationResourceToken() != null
        result.getReservationResourceToken().getWsId() == "validWsId"
        result.getBookResourceToken() != null
        result.getBookResourceToken().getBookInfoStr() == "validBookInfoStr"
    }
}
