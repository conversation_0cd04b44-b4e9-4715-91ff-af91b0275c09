package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookBaseInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookHotelInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookingRulesType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryBookingRulesType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.agg.hotel.roomavailable4createorder.entity.BaseInfo
import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.ArriveTimeInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ArriveTimeToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BookInitResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BookResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ServiceChargeResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum
import com.ctrip.corp.bff.hotel.book.common.signature.param.BookInfoBO
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfParamValid
import com.ctrip.corp.bff.hotel.book.qconfig.entity.MemberBonusRuleEntry
import com.ctrip.corp.bff.hotel.book.qconfig.entity.QconfigEntityOfGroupHotelIdAndRuleMapping
import com.ctrip.corp.bff.hotel.book.qconfig.entity.QconfigEntityOfGroupHotelMemberCardRule
import com.ctrip.corp.bff.hotel.book.contract.MembershipInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.sharkmock.SharkMockUtil
import com.ctrip.corp.corpsz.configuration.common.contract.CustomConfigSearchResponseType
import com.ctrip.corp.corpsz.corpcustomrequirementservice.common.contract.CheckResult
import com.ctrip.corp.corpsz.corpcustomrequirementservice.common.contract.ExternalDataCheckResponseType
import com.ctrip.ibu.platform.shark.sdk.api.Shark
import com.ctrip.soa._21234.BasicInfo
import com.ctrip.soa._21234.SearchTripDetailResponseType
import corp.user.service.CorpAccountQueryService.GeneralAccountInfo
import corp.user.service.CorpAccountQueryService.GeneralBatchSearchAccountInfoResponseType
import corp.user.service.CorpAccountQueryService.QueryIndividualAccountResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

class MapperOfParamValidResponseTest extends Specification {

    def myTestClass = Spy(new MapperOfParamValidResponse())

    def savePoint = new SavePoint()

    void setup() {
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        new MockUp<Shark>() {
            @Mock
            public static String getByLocale(Integer appId, String key, String locale) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        new MockUp<LogUtil>() {
            @Mock
            public void loggingClogOnly(LogLevelEnum level,
                                        Class<?> type,
                                        String title,
                                        String message,
                                        Throwable throwable,
                                        Map<String, String> indexTags) {

            }
        }
    }

    void cleanup() {
        savePoint.rollback()
    }


    @Unroll
    def "checkGroupHotelMemberCardRuleException"() {
        given:
        def req = new OrderCreateRequestType()
        req.setMembershipInfo(new MembershipInfo())
        req.getMembershipInfo().setGroupUserMembershipId("hotelVipCardID")
        req.getMembershipInfo().setMembershipNo(vipCardId)
        req.getMembershipInfo().setMembershipUid("uid")
        def context = new QueryCheckAvailContextResponseType()
        context.setHotelInfo(new BookHotelInfoEntity())
        context.getHotelInfo().setHotelGroupId(123)
        context.roomInfo = new BookRoomInfoEntity(gdsType: "Amadues")
        def rule = new QconfigEntityOfGroupHotelMemberCardRule()
        List<QconfigEntityOfGroupHotelIdAndRuleMapping> groupHotelIdAndRuleMappings = new ArrayList<QconfigEntityOfGroupHotelIdAndRuleMapping>()
        QconfigEntityOfGroupHotelIdAndRuleMapping mapping = new QconfigEntityOfGroupHotelIdAndRuleMapping()
        mapping.setCardIdRequired(true)
        mapping.setHotelGroupId(hotelGroupId)
        mapping.setCardIdLength(length)
        mapping.setLetterRequired(letterRequired)
        groupHotelIdAndRuleMappings.add(mapping)
        rule.setGroupHotelIdAndRuleMappings(groupHotelIdAndRuleMappings)
        when:
        def rst = myTestClass.checkMembershipCardNumFormat(req, context)
        then:
        rst.getErrorMessage() == mes
        where:
        hotelGroupId | vipCardId                                                                        | length | letterRequired | mes
        123          | "111189089078978978978978978978978789789789789789789789789789789789786786786786" | 5      | true           | "membershipNo error"
        123          | "1111("                                                                          | 4      | true           | "membershipNo error"
    }

    def "checkAmadeusCardNo"() {
        given:
        def req = new OrderCreateRequestType(membershipInfo: new MembershipInfo(membershipNo: no))
        def context = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(onlyGroupMemberCanBook: onlyGroupMemberCanBook), hotelInfo: new BookHotelInfoEntity(hotelGroupId: 1))
        expect:
        myTestClass.checkAmadeusCardNo(req, context, qconfig)?.friendlyMessage == res
        where:
        onlyGroupMemberCanBook | no     | qconfig                                                                                                 || res
        true                   | "1"    | [new MemberBonusRuleEntry(groupId: "1", minLength: 2, maxLength: 2, inputPattern: "NUMBER")]            || "会员卡号由【2】位数字组成，请输入正确卡号"
        true                   | "123"  | [new MemberBonusRuleEntry(groupId: "1", minLength: 2, maxLength: 2, inputPattern: "NUMBER")]            || "会员卡号由【2】位数字组成，请输入正确卡号"
        true                   | "123a" | [new MemberBonusRuleEntry(groupId: "1", minLength: 2, maxLength: 2, inputPattern: "NUMBER")]            || "会员卡号由【2】位数字组成，请输入正确卡号"
        true                   | "1a"   | [new MemberBonusRuleEntry(groupId: "1", minLength: 2, maxLength: 2, inputPattern: "NUMBER")]            || "会员卡号由【2】位数字组成，请输入正确卡号"
        true                   | "aa"   | [new MemberBonusRuleEntry(groupId: "1", minLength: 2, maxLength: 2, inputPattern: "NUMBER")]            || "会员卡号由【2】位数字组成，请输入正确卡号"
        true                   | "1"    | [new MemberBonusRuleEntry(groupId: "1", minLength: 2, maxLength: 3, inputPattern: "NUMBER")]            || "会员卡号由【2-3】位数字组成，请输入正确卡号"
        true                   | "12a"  | [new MemberBonusRuleEntry(groupId: "1", minLength: 2, maxLength: 3, inputPattern: "NUMBER")]            || "会员卡号由【2-3】位数字组成，请输入正确卡号"
        true                   | "1aa"  | [new MemberBonusRuleEntry(groupId: "1", minLength: 2, maxLength: 3, inputPattern: "NUMBER")]            || "会员卡号由【2-3】位数字组成，请输入正确卡号"
        true                   | "aaa"  | [new MemberBonusRuleEntry(groupId: "1", minLength: 2, maxLength: 3, inputPattern: "NUMBER")]            || "会员卡号由【2-3】位数字组成，请输入正确卡号"
        true                   | "1"    | [new MemberBonusRuleEntry(groupId: "1", minLength: 2, maxLength: 3, inputPattern: "NUMBER_AND_LETTER")] || "会员卡号由【2-3】位数字和字母共同组成，请输入正确卡号"
        true                   | "11"   | [new MemberBonusRuleEntry(groupId: "1", minLength: 2, maxLength: 3, inputPattern: "NUMBER_AND_LETTER")] || "会员卡号由【2-3】位数字和字母共同组成，请输入正确卡号"
        true                   | "111"  | [new MemberBonusRuleEntry(groupId: "1", minLength: 2, maxLength: 3, inputPattern: "NUMBER_AND_LETTER")] || "会员卡号由【2-3】位数字和字母共同组成，请输入正确卡号"
        true                   | "1"    | [new MemberBonusRuleEntry(groupId: "1", minLength: 2, maxLength: 2, inputPattern: "NUMBER_AND_LETTER")] || "会员卡号由【2】位数字和字母共同组成，请输入正确卡号"
        true                   | "11"   | [new MemberBonusRuleEntry(groupId: "1", minLength: 2, maxLength: 2, inputPattern: "NUMBER_AND_LETTER")] || "会员卡号由【2】位数字和字母共同组成，请输入正确卡号"
        false                  | "11"   | [new MemberBonusRuleEntry(groupId: "1", minLength: 2, maxLength: 2, inputPattern: "NUMBER_AND_LETTER")] || "会员卡号由【2】位数字和字母共同组成，请输入正确卡号"
        false                  | "11"   | [null]                                                                                                  || null
        false                  | ""     | [new MemberBonusRuleEntry(groupId: "1", minLength: 2, maxLength: 2, inputPattern: "NUMBER_AND_LETTER")] || null

    }

    def "checkAmadeusCardNo0"() {
        given:
        def context = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(onlyGroupMemberCanBook: false), hotelInfo: new BookHotelInfoEntity(hotelGroupId: 1))
        def req = new OrderCreateRequestType(membershipInfo: new MembershipInfo(membershipNo: "1"))
        expect:
        myTestClass.checkAmadeusCardNo(req, context, null) == null

    }


    @Unroll
    def "testWhOnlyCorp with #description"() {
        given: "Mock dependencies and inputs"
        def mapper = new MapperOfParamValidResponse()
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(new QueryCheckAvailContextResponseType(
                                roomInfo: new BookRoomInfoEntity(balanceType: "PP", roomType: "M"),
                                bookingRules: new QueryBookingRulesType(restrictRuleList: Arrays.asList("EMPLOYEE_BOOKING"))))
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()
        def orderCreateRequestType = new OrderCreateRequestType(hotelBookPassengerInputs: Arrays.asList(new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(employee: "F"))))

        when: "Calling whOnlyCorp method"
        myTestClass.whOnlyCorp(checkAvailInfo, orderCreateRequestType)

        then: "The result should match the expected outcome"
        def exception = thrown(BusinessException)
        exception.errorCode == 685
        exception.friendlyMessage == "该房型仅限员工入住，当前入住人包含非员工，请更换其它房型"
    }

    @Unroll
    def "test checkCheckInCheckOut with #description"() {
        given: "Mock dependencies and inputs"
        def mapper = new MapperOfParamValidResponse()
        def orderCreateRequestType = new OrderCreateRequestType(
                hotelBookInput: new HotelBookInput(
                        hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: checkIn, checkOut: checkOut)
                )
        )
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(
                baseInfo: new BookBaseInfoEntity(startTime: startTime, endTime: endTime)
        )

        when: "Calling checkCheckInCheckOut method"
        def result = true
        def exception = null
        try {
            result = mapper.checkCheckInCheckOut(orderCreateRequestType, queryCheckAvailContextResponseType)
        } catch (BusinessException e) {
            exception = e
        }

        then: "The result should match the expected outcome"
        if (expectedResult) {
            assert result == expectedResult
        } else {
            assert exception != null
            assert exception.errorCode == OrderCreateErrorEnum.INVALID_BOOK_INFO.errorCode
        }

        where:
        description                           | checkIn      | checkOut     | startTime                | endTime                  || expectedResult
        "valid check-in and check-out"        | "2024-06-23" | "2024-06-25" | "2024-06-23T00:00:00+08" | "2024-06-25T00:00:00+08" || true
        "invalid check-in date"               | "2024-06-22" | "2024-06-25" | "2024-06-23T00:00:00+08" | "2024-06-25T00:00:00+08" || false
        "invalid check-out date"              | "2024-06-23" | "2024-06-26" | "2024-06-23T00:00:00+08" | "2024-06-25T00:00:00+08" || false
        "both check-in and check-out valid"   | "2024-06-23" | "2024-06-25" | "2024-06-23T00:00:00+08" | "2024-06-25T00:00:00+08" || true
        "both check-in and check-out invalid" | "2024-06-22" | "2024-06-26" | "2024-06-23T00:00:00+08" | "2024-06-25T00:00:00+08" || false
    }


    @Unroll
    def "testCheckArriveTime with #description"() {
        given: "Mock dependencies and inputs"
        def orderCreateRequestType = new OrderCreateRequestType(
                arriveTimeInput: new ArriveTimeInput(arriveTimeToken: arriveTimeToken)
        )
        def resourceToken = new ResourceToken(
                reservationResourceToken: new ReservationResourceToken(arriveTimeKey: arriveTimeKey)
        )

        when: "Calling checkArriveTime method"
        def result = true
        def exception = null
        try {
            result = myTestClass.checkArriveTime(orderCreateRequestType, resourceToken)
        } catch (BusinessException e) {
            exception = e
        }

        then: "The result should match the expected outcome"
        if (expectedResult) {
            assert result == expectedResult
        } else {
            assert exception != null
            assert exception.errorCode == OrderCreateErrorEnum.INVALID_BOOK_INFO.errorCode
        }

        where:
        description                  | arriveTimeToken                                                                                                | arriveTimeKey                                                                                                  | expectedResult
        "valid arrive time"          | TokenParseUtil.generateToken(new ArriveTimeToken(arriveTimeUTC: "2023-09-08T12:12:33"), ArriveTimeToken.class) | TokenParseUtil.generateToken(new ArriveTimeToken(arriveTimeUTC: "2023-09-08T12:12:33"), ArriveTimeToken.class) | true
        "valid arrive time"          | TokenParseUtil.generateToken(new ArriveTimeToken(arriveTimeUTC: "2023-09-08T12:12:33"), ArriveTimeToken.class) | TokenParseUtil.generateToken(new ArriveTimeToken(arriveTimeUTC: "2023-09-08T12:12:35"), ArriveTimeToken.class) | false
        "both arrive times are null" | null                                                                                                           | null                                                                                                           | true
        "arrive time token is null"  | null                                                                                                           | TokenParseUtil.generateToken(new ArriveTimeToken(arriveTimeUTC: "2023-09-08T12:12:33"), ArriveTimeToken.class) | true
        "arrive time key is null"    | TokenParseUtil.generateToken(new ArriveTimeToken(arriveTimeUTC: "2023-09-08T12:12:33"), ArriveTimeToken.class) | null                                                                                                           | true
    }


    @Unroll
    def "testcheckBookInfoBO with #description"() {
        given: "Mock dependencies and inputs"
        def orderCreateRequestType = new OrderCreateRequestType(
                hotelPayTypeInput: hotelPayTypeInput
        )
        def resourceToken = new ResourceToken(
                bookResourceToken: new BookResourceToken(bookInfoStr: bookInfoStr, bookVersion: bookVersion)
        )

        when: "Calling BookResourceToken method"
        def result = null
        def exception = null
        try {
            result = myTestClass.checkBookInfoBO(orderCreateRequestType, resourceToken)
        } catch (BusinessException e) {
            exception = e
        }

        then: "The result should match the expected outcome"
        if (expectedResult) {
            assert result == expectedResult
        } else {
            assert exception != null
            assert exception.errorCode == OrderCreateErrorEnum.INVALID_BOOK_INFO.errorCode
        }

        where:
        description                             | hotelPayTypeInput                                                                                                                          | bookInfoStr                                                                                                            | bookVersion | expectedResult
        "roomPayType not equals"                | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY"))                                                                 | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "CORP_PAY"))                             | "2"         | true
        "roomPayType not equals"                | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY"))                                                                 | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "SELF_PAY"))                             | "2"         | false
        "roomPayType not equals"                | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY"))                                                                 | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "SELF_PAY"))                             | "3"         | true
        "bookVersion not equals"                | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY"))                                                                 | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "CORP_PAY"))                             | "3"         | true
        "servicePayType not equals"             | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CASH"), new HotelPayTypeInput(payCode: "SERVICE", payType: "CORP_PAY"))     | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "CASH", servicePayType: "SELF_PAY"))     | "2"         | false
        "roomPayType and servicePayType equals" | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY"), new HotelPayTypeInput(payCode: "SERVICE", payType: "CORP_PAY")) | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "CORP_PAY", servicePayType: "CORP_PAY")) | "2"         | true
    }

    @Unroll
    def "testcheckBookInfoBO_comprehensive with #description"() {
        given: "Mock dependencies and inputs for comprehensive testing"
        def orderCreateRequestType = new OrderCreateRequestType(
                hotelPayTypeInput: hotelPayTypeInput
        )
        def resourceToken = new ResourceToken(
                bookResourceToken: new BookResourceToken(bookInfoStr: bookInfoStr, bookVersion: bookVersion)
        )

        // Mock the buildServicePayType method to return the expected service pay type
        def mapper = Spy(MapperOfParamValidResponse)
        mapper.buildServicePayType(_, _) >> servicePayType

        when: "Calling checkBookInfoBO method"
        def result = null
        def exception = null
        try {
            result = mapper.checkBookInfoBO(orderCreateRequestType, resourceToken)
        } catch (BusinessException e) {
            exception = e
        }

        then: "The result should match the expected outcome"
        if (expectedException) {
            assert exception != null
            assert exception.errorCode == OrderCreateErrorEnum.INVALID_BOOK_INFO.errorCode
        } else {
            assert result == true
            assert exception == null
        }

        where:
        description                                    | hotelPayTypeInput                                                                                                                          | bookInfoStr                                                                                                            | bookVersion | servicePayType                    | expectedException
        "null bookResourceToken"                       | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY"))                                                                 | null                                                                                                                   | "3"         | HotelPayTypeEnum.CORP_PAY         | false
        "version mismatch - old version"               | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY"))                                                                 | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "CORP_PAY"))                             | "2"         | HotelPayTypeEnum.CORP_PAY         | false
        "room pay type mismatch"                       | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY"))                                                                 | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "SELF_PAY"))                             | "3"         | HotelPayTypeEnum.CORP_PAY         | true
        "service pay type mismatch - CORP_PAY"         | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY"))                                                                 | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "CORP_PAY", servicePayType: "SELF_PAY")) | "3"         | HotelPayTypeEnum.CORP_PAY         | true
        "service pay type mismatch - SELF_PAY"         | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY"))                                                                 | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "CORP_PAY", servicePayType: "CORP_PAY")) | "3"         | HotelPayTypeEnum.SELF_PAY         | true
        "service pay type not in validation list"      | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY"))                                                                 | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "CORP_PAY", servicePayType: "CORP_PAY")) | "3"         | HotelPayTypeEnum.CASH             | false
        "all pay types match - CORP_PAY"               | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY"))                                                                 | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "CORP_PAY", servicePayType: "CORP_PAY")) | "3"         | HotelPayTypeEnum.CORP_PAY         | false
        "all pay types match - SELF_PAY"               | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "SELF_PAY"))                                                                 | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "SELF_PAY", servicePayType: "SELF_PAY")) | "3"         | HotelPayTypeEnum.SELF_PAY         | false
        "corporate card pay converted to corp pay"     | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY"))                                                                 | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "CORP_PAY", servicePayType: "CORP_PAY")) | "3"         | HotelPayTypeEnum.CORPORATE_CARD_PAY | false
    }

    @Unroll
    def "testcheckBookInfoBO_checkBookInfoCall with #description"() {
        given: "Mock dependencies and inputs to test checkBookInfo method call"
        def orderCreateRequestType = new OrderCreateRequestType(
                hotelPayTypeInput: hotelPayTypeInput
        )
        def resourceToken = new ResourceToken(
                bookResourceToken: new BookResourceToken(bookInfoStr: bookInfoStr, bookVersion: bookVersion)
        )

        // Create a spy to verify checkBookInfo is called
        def mapper = Spy(MapperOfParamValidResponse)
        mapper.buildServicePayType(_, _) >> servicePayType
        mapper.checkBookInfo(_, _) >> { args ->
            if (checkBookInfoThrowsException) {
                throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.INVALID_BOOK_INFO,
                    OrderCreateErrorEnum.INVALID_BOOK_INFO.getErrorMessage())
            }
            return true
        }

        when: "Calling checkBookInfoBO method"
        def result = null
        def exception = null
        try {
            result = mapper.checkBookInfoBO(orderCreateRequestType, resourceToken)
        } catch (BusinessException e) {
            exception = e
        }

        then: "Verify checkBookInfo method is called and result matches expectation"
        if (shouldCallCheckBookInfo) {
            1 * mapper.checkBookInfo(orderCreateRequestType, resourceToken)
        } else {
            0 * mapper.checkBookInfo(_, _)
        }

        if (expectedException) {
            assert exception != null
            assert exception.errorCode == OrderCreateErrorEnum.INVALID_BOOK_INFO.errorCode
        } else {
            assert result == true
            assert exception == null
        }

        where:
        description                                    | hotelPayTypeInput                                                                                                                          | bookInfoStr                                                                                                            | bookVersion | servicePayType                    | checkBookInfoThrowsException | shouldCallCheckBookInfo | expectedException
        "checkBookInfo called and succeeds"           | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY"))                                                                 | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "CORP_PAY", servicePayType: "CORP_PAY")) | "3"         | HotelPayTypeEnum.CORP_PAY         | false                        | true                    | false
        "checkBookInfo called and throws exception"   | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY"))                                                                 | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "CORP_PAY", servicePayType: "CORP_PAY")) | "3"         | HotelPayTypeEnum.CORP_PAY         | true                         | true                    | true
        "checkBookInfo not called - version mismatch" | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY"))                                                                 | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "CORP_PAY", servicePayType: "CORP_PAY")) | "2"         | HotelPayTypeEnum.CORP_PAY         | false                        | false                   | false
        "checkBookInfo not called - room pay mismatch"| Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY"))                                                                 | OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "SELF_PAY", servicePayType: "CORP_PAY")) | "3"         | HotelPayTypeEnum.CORP_PAY         | false                        | false                   | true
    }

    @Unroll
    def "testBuildServicePayType with #description"() {
        given: "Mock dependencies and inputs for buildServicePayType testing"
        def orderCreateRequestType = new OrderCreateRequestType(
                hotelPayTypeInput: hotelPayTypeInput
        )
        def resourceToken = new ResourceToken(
                bookResourceToken: new BookResourceToken(bookInfoStr: "test", bookVersion: "3")
        )

        when: "Calling buildServicePayType method"
        def result = myTestClass.buildServicePayType(orderCreateRequestType, resourceToken)

        then: "The result should match the expected service pay type"
        result == expectedServicePayType

        where:
        description                                    | hotelPayTypeInput                                                                                                                          | expectedServicePayType
        "corporate card pay converted to corp pay"    | Arrays.asList(new HotelPayTypeInput(payCode: "SERVICE", payType: "CORPORATE_CARD_PAY"))                                                    | HotelPayTypeEnum.CORP_PAY
        "corp pay remains corp pay"                   | Arrays.asList(new HotelPayTypeInput(payCode: "SERVICE", payType: "CORP_PAY"))                                                              | HotelPayTypeEnum.CORP_PAY
        "self pay remains self pay"                   | Arrays.asList(new HotelPayTypeInput(payCode: "SERVICE", payType: "SELF_PAY"))                                                              | HotelPayTypeEnum.SELF_PAY
        "cash pay remains cash pay"                   | Arrays.asList(new HotelPayTypeInput(payCode: "SERVICE", payType: "CASH"))                                                                  | HotelPayTypeEnum.CASH
        "no service pay type input"                   | Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY"))                                                                 | null
        "empty pay type input list"                   | []                                                                                                                                         | null
    }

    @Unroll
    def "testcheckBookInfoBO_edgeCases with #description"() {
        given: "Mock dependencies and inputs for edge case testing"
        def orderCreateRequestType = orderCreateRequest
        def resourceToken = resourceTokenInput

        when: "Calling checkBookInfoBO method"
        def result = null
        def exception = null
        try {
            result = myTestClass.checkBookInfoBO(orderCreateRequestType, resourceToken)
        } catch (BusinessException e) {
            exception = e
        }

        then: "The result should match the expected outcome"
        if (expectedException) {
            assert exception != null
        } else {
            assert result == expectedResult
            assert exception == null
        }

        where:
        description                                    | orderCreateRequest                                                                                                                         | resourceTokenInput                                                                                                     | expectedResult | expectedException
        "null resourceToken"                          | new OrderCreateRequestType(hotelPayTypeInput: Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY")))                | null                                                                                                                   | true           | false
        "null bookResourceToken"                      | new OrderCreateRequestType(hotelPayTypeInput: Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY")))                | new ResourceToken(bookResourceToken: null)                                                                            | true           | false
        "null bookInfoStr"                            | new OrderCreateRequestType(hotelPayTypeInput: Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY")))                | new ResourceToken(bookResourceToken: new BookResourceToken(bookInfoStr: null, bookVersion: "3"))                    | true           | false
        "empty bookInfoStr"                           | new OrderCreateRequestType(hotelPayTypeInput: Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY")))                | new ResourceToken(bookResourceToken: new BookResourceToken(bookInfoStr: "", bookVersion: "3"))                      | true           | false
        "null bookVersion"                            | new OrderCreateRequestType(hotelPayTypeInput: Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY")))                | new ResourceToken(bookResourceToken: new BookResourceToken(bookInfoStr: "test", bookVersion: null))                 | true           | false
        "empty bookVersion"                           | new OrderCreateRequestType(hotelPayTypeInput: Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY")))                | new ResourceToken(bookResourceToken: new BookResourceToken(bookInfoStr: "test", bookVersion: ""))                   | true           | false
        "null hotelPayTypeInput"                      | new OrderCreateRequestType(hotelPayTypeInput: null)                                                                                       | new ResourceToken(bookResourceToken: new BookResourceToken(bookInfoStr: OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "CORP_PAY")), bookVersion: "3")) | true | false
        "empty hotelPayTypeInput"                     | new OrderCreateRequestType(hotelPayTypeInput: [])                                                                                         | new ResourceToken(bookResourceToken: new BookResourceToken(bookInfoStr: OrderCreateProcessorOfUtil.buildSignatureBookInfo(new BookInfoBO(roomPayType: "CORP_PAY")), bookVersion: "3")) | true | false
    }

    @Unroll
    def "testCheckInfoId with #description"() {
        given: "A HotelPassengerInput instance"
        HotelBookPassengerInput hotelBookPassengerInput = new HotelBookPassengerInput(saveProfileFlag: saveProfileFlag)
        def hotelPassengerInput = new HotelPassengerInput(employee: employee, infoId: infoId)
        hotelBookPassengerInput.hotelPassengerInput = hotelPassengerInput

        expect: "Calling checkInfoId method"
        result == myTestClass.checkInfoId(hotelBookPassengerInput)

        where:
        description                      | employee | saveProfileFlag | infoId  | result
        "employee with valid infoId"     | "T"      | null            | "12345" | true
        "employee with null infoId"      | "T"      | null            | null    | true
        "non-employee with valid infoId" | "F"      | null            | "12345" | true
        "non-employee with null infoId"  | "F"      | null            | null    | false
        "non-employee with non-numeric"  | "F"      | null            | "abc"   | false
        "non-employee with null infoId"  | "F"      | "F"             | null    | true
        "non-employee with null infoId"  | "F"      | "T"             | null    | false
    }

    @Unroll
    def "testCheckApprovalInput with #description"() {
        given: "An OrderCreateRequestType instance and AccountInfo instance"
        def orderCreateRequestType = new OrderCreateRequestType(
                corpPayInfo: new CorpPayInfo(corpPayType: "public"),
                approvalInput: approvalInput,
                hotelPayTypeInput: Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: payType))
        )
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
        }
        accountInfo.isOaApprovalHead() >> isOaApprovalHead
        accountInfo.isPreApprovalRequired(_ as Boolean, _ as CorpPayInfo) >> isPreApprovalRequired

        when:
        def result = null
        def exception = null
        try {
            result = myTestClass.checkApprovalInput(orderCreateRequestType, accountInfo, new HashMap<String, StrategyInfo>())
        } catch (BusinessException e) {
            exception = e
        }

        then:
        if (expectedResult) {
            assert result == expectedResult
        } else {
            assert exception != null
            assert exception.errorCode == OrderCreateErrorEnum.MISS_SUBAPPROVALNO.errorCode
        }

        where:
        description               | approvalInput                           | isOaApprovalHead | isPreApprovalRequired | payType          | expectedResult
        "approvalInput is null"   | null                                    | false            | false                 | "CORP_PAY"       | true
        "approvalInput has value" | new ApprovalInput(subApprovalNo: "123") | false            | false                 | "CORP_PAY"       | true
        "approvalInput is null"   | null                                    | true             | true                  | "SELF_PAY"       | false
        "approvalInput is null"   | new ApprovalInput(emergency: "T")       | true             | true                  | "SELF_PAY"       | true
        "approvalInput is null"   | null                                    | false            | true                  | "SELF_PAY"       | true
    }

    @Unroll
    def "testCheckApprovalInput with #description-POST"() {
        given: "An OrderCreateRequestType instance and AccountInfo instance"
        def orderCreateRequestType = new OrderCreateRequestType(
                corpPayInfo: new CorpPayInfo(corpPayType: "public"),
                approvalInput: approvalInput,
                hotelPayTypeInput: Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: payType))
        )
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
        }
        accountInfo.isOaApprovalHead() >> isOaApprovalHead
        accountInfo.isPreApprovalRequired(_ as Boolean, _ as CorpPayInfo) >> isPreApprovalRequired

        when:
        def result = null
        def exception = null
        try {
            result = myTestClass.checkApprovalInput(orderCreateRequestType, accountInfo, new HashMap<String, StrategyInfo>())
        } catch (BusinessException e) {
            exception = e
        }

        then:
        if (expectedResult) {
            assert result == expectedResult
        } else {
            assert exception != null
            assert exception.errorCode == OrderCreateErrorEnum.MISS_SUBAPPROVALNO_POST.errorCode
        }

        where:
        description               | approvalInput                           | isOaApprovalHead | isPreApprovalRequired | payType          | expectedResult
        "approvalInput is null"   | null                                    | false            | false                 | "CORP_PAY"       | true
        "approvalInput has value" | new ApprovalInput(subApprovalNo: "123") | false            | false                 | "CORP_PAY"       | true
        "approvalInput is null"   | null                                    | false            | true                  | "CORP_PAY"       | false
        "approvalInput is null"   | null                                    | false            | true                  | "MIX_PAY"        | false
        "approvalInput is null"   | null                                    | false            | true                  | "FLASH_STAY_PAY" | false
        "approvalInput is null"   | null                                    | false            | true                  | "SELF_PAY"       | true
    }

    @Unroll
    def "buildHasApprovalInfo"() {
        given:
        expect:
        result == myTestClass.buildHasApprovalInfo(hotelBookPassengerInput)
        where:
        hotelBookPassengerInput                                                                                                                     || result
        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(approvalInput: new ApprovalInput(emergency: "T")))                 || true
        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(approvalInput: new ApprovalInput(subApprovalNo: "subApprovalNo"))) || true
        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput())                                                                 || false
    }

    def "buildPreApprovalMap"() {
        given:
        GeneralBatchSearchAccountInfoResponseType generalBatchSearchAccountInfoResponseType = new GeneralBatchSearchAccountInfoResponseType(
                results: [new GeneralAccountInfo(uid: "uid", results: ["isChkaheadapproveHotelI": "T"]),
                          new GeneralAccountInfo(uid: "uid2", results: ["isChkaheadapproveHotelI": "F"])])
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        when:
        def result = myTestClass.buildPreApprovalMap(generalBatchSearchAccountInfoResponseType, orderCreateRequestType)
        then:
        result.size() == 2
        result.get("uid")
        !result.get("uid2")


        when:
        orderCreateRequestType.cityInput = new CityInput(cityId: 22249)
        generalBatchSearchAccountInfoResponseType = new GeneralBatchSearchAccountInfoResponseType(
                results: [new GeneralAccountInfo(uid: "uid", results: ["isChkaheadapproveHotel": "T"]),
                          new GeneralAccountInfo(uid: "uid2", results: ["isChkaheadapproveHotel": "F"])])
        result = myTestClass.buildPreApprovalMap(generalBatchSearchAccountInfoResponseType, orderCreateRequestType)
        then:
        result.size() == 2
        result.get("uid")
        !result.get("uid2")
    }


    @Unroll
    def "checkPassengerApprovalInfoByPassenger"() {
        given:
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            preApprovalStrategyByPsg(_ as Boolean, _ as CorpPayInfo) >> true
        }
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                hotelBookPassengerInputs: hotelBookPassengerInputs,
                corpPayInfo: new CorpPayInfo(corpPayType: "public"),
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "loginuid")))
        GeneralBatchSearchAccountInfoResponseType generalBatchSearchAccountInfoResponseType = new GeneralBatchSearchAccountInfoResponseType(
                results: [new GeneralAccountInfo(uid: "p1uid", results: ["isChkaheadapproveHotelI": "T"]),
                          new GeneralAccountInfo(uid: "p2uid", results: ["isChkaheadapproveHotelI": "F"]),
                          new GeneralAccountInfo(uid: "p3infoid", results: ["isChkaheadapproveHotelI": "T"]),
                          new GeneralAccountInfo(uid: "loginuid", results: ["isChkaheadapproveHotelI": "T"])])
        when:
        def exception = null
        try {
            myTestClass.checkPassengerApprovalInfoByPassenger(accountInfo, orderCreateRequestType, generalBatchSearchAccountInfoResponseType)
        } catch (BusinessException e) {
            exception = e
        }
        then:
        if (expectedResult) {
            assert exception == null
        } else {
            assert exception != null
            assert exception.errorCode == OrderCreateErrorEnum.PSG_MISS_SUBAPPROVALNO.errorCode
        }
        where:
        hotelBookPassengerInputs                                                                                                                                                         | expectedResult
        [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "p1uid", employee: "T", approvalInput: new ApprovalInput(subApprovalNo: "subApprovalNo"))),
         new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "p2uid", employee: "T")),
         new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "p3infoid", employee: "F", approvalInput: new ApprovalInput(subApprovalNo: "subApprovalNo")))] | true
        [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "p1uid", employee: "T", approvalInput: new ApprovalInput(subApprovalNo: "subApprovalNo"))),
         new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "p2uid", employee: "T")),
         new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "p3infoid", employee: "F"))]                                                                   | false
        [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "p1uid", employee: "T")),
         new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "p2uid", employee: "T")),
         new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "p3infoid", employee: "F", approvalInput: new ApprovalInput(subApprovalNo: "subApprovalNo")))] | false
    }


    @Unroll
    def "checkPassengerApprovalInfoByLoginUid"() {
        given:
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            preApprovalStrategyByPsg(_ as Boolean, _ as CorpPayInfo) >> false
            preApprovalChooseByPsg(_ as Boolean, _ as CorpPayInfo) >> true
            preApprovalSameTrip(_ as Boolean, _ as CorpPayInfo) >> false
        }
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                hotelBookPassengerInputs: [hotelBookPassengerInput],
                corpPayInfo: new CorpPayInfo(corpPayType: "public"),
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "loginuid")))
        when:
        def exception = null
        try {
            myTestClass.checkPassengerApprovalInfoByLoginUid(accountInfo, orderCreateRequestType)
        } catch (BusinessException e) {
            exception = e
        }
        then:
        if (expectedResult) {
            assert exception == null
        } else {
            assert exception != null
            assert exception.errorCode == OrderCreateErrorEnum.PSG_MISS_SUBAPPROVALNO.errorCode
        }
        where:
        hotelBookPassengerInput                                                                                                                     | expectedResult
        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(approvalInput: new ApprovalInput(subApprovalNo: "subApprovalNo"))) | true
        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(approvalInput: new ApprovalInput(subApprovalNo: "")))              | false
        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(approvalInput: new ApprovalInput(emergency: "T")))                 | true
        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput())                                                                 | false
    }

    @Unroll
    def "checkPassengerApprovalInfoByLoginUid-preApprovalStrategyByPsg"() {
        given:
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            preApprovalStrategyByPsg(_ as Boolean, _ as CorpPayInfo) >> true
            preApprovalChooseByPsg(_ as Boolean, _ as CorpPayInfo) >> false
            preApprovalSameTrip(_ as Boolean, _ as CorpPayInfo) >> false
        }
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                hotelBookPassengerInputs: [hotelBookPassengerInput],
                corpPayInfo: new CorpPayInfo(corpPayType: "public"),
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "loginuid")))
        when:
        def exception = null
        try {
            myTestClass.checkPassengerApprovalInfoByLoginUid(accountInfo, orderCreateRequestType)
        } catch (BusinessException e) {
            exception = e
        }
        then:
        if (expectedResult) {
            assert exception == null
        } else {
            assert exception != null
            assert exception.errorCode == OrderCreateErrorEnum.PSG_MISS_SUBAPPROVALNO.errorCode
        }
        where:
        hotelBookPassengerInput                                                                                                                     | expectedResult
        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(approvalInput: new ApprovalInput(subApprovalNo: "")))              | true
        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput())                                                                 | true
    }


    @Unroll
    def "checkPassengerApprovalInfoByLoginUid-preApprovalChooseByPsg-preApprovalSameTrip"() {
        given:
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            preApprovalStrategyByPsg(_ as Boolean, _ as CorpPayInfo) >> false
            preApprovalChooseByPsg(_ as Boolean, _ as CorpPayInfo) >> true
            preApprovalSameTrip(_ as Boolean, _ as CorpPayInfo) >> true
        }
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                hotelBookPassengerInputs: [hotelBookPassengerInput],
                corpPayInfo: new CorpPayInfo(corpPayType: "public"),
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "loginuid")))
        when:
        def exception = null
        try {
            myTestClass.checkPassengerApprovalInfoByLoginUid(accountInfo, orderCreateRequestType)
        } catch (BusinessException e) {
            exception = e
        }
        then:
        if (expectedResult) {
            assert exception == null
        } else {
            assert exception != null
            assert exception.errorCode == OrderCreateErrorEnum.PSG_MISS_SUBAPPROVALNO.errorCode
        }
        where:
        hotelBookPassengerInput                                                                                                                     | expectedResult
        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(approvalInput: new ApprovalInput(subApprovalNo: "")))              | true
        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput())                                                                 | true
    }

    @Unroll
    def "externalDataCheck-PSG_EXTERNAL_EMPLOYEE_ID_ERROR"() {
        given:
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            public static boolean needExternalEmployeeId(CustomConfigSearchResponseType customConfigSearchResponseType,
                                                         IntegrationSoaRequestType integrationSoaRequestType) {
                return true
            }
        }
        ExternalDataCheckResponseType externalDataCheckResponseType = new ExternalDataCheckResponseType(
                checkResultList: [
                        new CheckResult(result: result1, message: "员工号1异常", dataValue: "员工号1"),
                        new CheckResult(result: result2, message: "员工号2异常", dataValue: "员工号2")
                ]
        )
        CustomConfigSearchResponseType customConfigSearchResponseType = new CustomConfigSearchResponseType()
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        when:
        def exception = null
        try {
            myTestClass.externalDataCheck(externalDataCheckResponseType, customConfigSearchResponseType, orderCreateRequestType)
        } catch (BusinessException e) {
            exception = e
        }
        then:
        if (expectedResult) {
            assert exception == null
        } else {
            assert exception != null
            assert exception.errorCode == OrderCreateErrorEnum.EXTERNAL_EMPLOYEE_ID_ERROR.errorCode
            assert exception.friendlyMessage == "员工号1、员工号2的企微号不存在，请确认并修改企微号"
        }
        where:
        result1 | result2 | expectedResult
        false   | false   | false
        true    | true    | true
    }
    @Unroll
    def "checkOrderTripPolicyId"() {
        given:
        def result

        when: "not match strategy"
        result = myTestClass.checkOrderTripPolicyId(new OrderCreateRequestType(strategyInfos: []), null, null)
        then:
        result == null

        when:
        def orderCreateRequestType = new OrderCreateRequestType(
                strategyInfos: [new StrategyInfo(strategyKey: "TRIPPOLICYID_MUST_SAME_ORDERPOLICYID", strategyValue: "T")]
        )
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isPolicyModel() >> false
        }
        result = myTestClass.checkOrderTripPolicyId(orderCreateRequestType, null, accountInfo)
        then:
        result == null

        when:
        def accountInfoPolicyMode = Mock(WrapperOfAccount.AccountInfo) {
            isPolicyModel() >> true
        }
        result = myTestClass.checkOrderTripPolicyId(orderCreateRequestType, null, accountInfoPolicyMode)
        then:
        result == null

        when:
        result = myTestClass.checkOrderTripPolicyId(orderCreateRequestType,
                new SearchTripDetailResponseType(basicInfo: new BasicInfo(policyUid: "123")), accountInfoPolicyMode)
        then:
        result == null


        when:
        result = myTestClass.checkOrderTripPolicyId(
                new OrderCreateRequestType(
                        hotelPolicyInput: new HotelPolicyInput(policyInput: new PolicyInput(policyUid: "123")),
                        strategyInfos: [new StrategyInfo(strategyKey: "TRIPPOLICYID_MUST_SAME_ORDERPOLICYID", strategyValue: "T")]),
                new SearchTripDetailResponseType(basicInfo: new BasicInfo(policyUid: "123")), accountInfoPolicyMode)
        then:
        result == null

        when:
        result = myTestClass.checkOrderTripPolicyId(
                new OrderCreateRequestType(
                        hotelPolicyInput: new HotelPolicyInput(policyInput: new PolicyInput(policyUid: "234")),
                        strategyInfos: [new StrategyInfo(strategyKey: "TRIPPOLICYID_MUST_SAME_ORDERPOLICYID", strategyValue: "T")]),
                new SearchTripDetailResponseType(basicInfo: new BasicInfo(policyUid: "123")), accountInfoPolicyMode)
        then:
        result != null
    }

    def "convert"() {
        given:
        WrapperOfParamValid wrapperOfParamValid = WrapperOfParamValid.builder()
                .withOrderCreateRequestType(new OrderCreateRequestType())
                .withResourceToken(new ResourceToken())
                .withAllocationResultToken(null)
                .withOrderCreateToken(new OrderCreateToken())
                .withAccountInfo(Mock(WrapperOfAccount.AccountInfo))
                .withQueryCheckAvailContextResponseType(new QueryCheckAvailContextResponseType())
                .withSearchTripDetailResponseType(new SearchTripDetailResponseType())
                .withQueryIndividualAccountResponseType(new QueryIndividualAccountResponseType())
                .withBaseCheckAvailInfo(Mock(WrapperOfCheckAvail.BaseCheckAvailInfo))
                .withMemberBonusRuleEntries([])
                .withGeneralBatchSearchAccountInfoResponseType(new GeneralBatchSearchAccountInfoResponseType())
                .withQconfigOfCertificateInitConfig(null)
                .withExternalDataCheckResponseType(new ExternalDataCheckResponseType())
                .withCustomConfigSearchResponseType(new CustomConfigSearchResponseType())
                .withStrategyInfoMap(new HashMap<String, StrategyInfo>())
                .build();
        when:
        def result = myTestClass.convert(Tuple1.of(wrapperOfParamValid))
        then:
        result
        wrapperOfParamValid.orderCreateRequestType != null
        wrapperOfParamValid.resourceToken != null
        wrapperOfParamValid.allocationResultToken == null
        wrapperOfParamValid.orderCreateToken != null
        wrapperOfParamValid.accountInfo != null
        wrapperOfParamValid.queryCheckAvailContextResponseType != null
        wrapperOfParamValid.searchTripDetailResponseType != null
        wrapperOfParamValid.queryIndividualAccountResponseType != null
        wrapperOfParamValid.baseCheckAvailInfo != null
        wrapperOfParamValid.memberBonusRuleEntries != null
        wrapperOfParamValid.generalBatchSearchAccountInfoResponseType != null
        wrapperOfParamValid.qconfigOfCertificateInitConfig == null
        wrapperOfParamValid.externalDataCheckResponseType != null
        wrapperOfParamValid.customConfigSearchResponseType != null
        wrapperOfParamValid.strategyInfoMap != null
    }

    def "buildServicePayType-CORPORATE_CARD_PAY"() {
        given:
        new MockUp<HotelPayTypeUtil>() {
            @Mock
            public static HotelPayTypeEnum getServicePayType(List<HotelPayTypeInput> hotelPayTypeInputs,
                                                             ResourceToken resourceToken) {
                return HotelPayTypeEnum.CORPORATE_CARD_PAY
            }
        }
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        ResourceToken resourceToken = new ResourceToken()
        when:
        def result = myTestClass.buildServicePayType(orderCreateRequestType, resourceToken)
        then:
        result == HotelPayTypeEnum.CORP_PAY
    }

    def "buildServicePayType"() {
        given:
        new MockUp<HotelPayTypeUtil>() {
            @Mock
            public static HotelPayTypeEnum getServicePayType(List<HotelPayTypeInput> hotelPayTypeInputs,
                                                             ResourceToken resourceToken) {
                return HotelPayTypeEnum.CORP_PAY
            }
        }
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        ResourceToken resourceToken = new ResourceToken()
        when:
        def result = myTestClass.buildServicePayType(orderCreateRequestType, resourceToken)
        then:
        result == HotelPayTypeEnum.CORP_PAY
    }

}
